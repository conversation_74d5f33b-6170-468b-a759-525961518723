{"name": "bookln", "version": "1.0.0", "private": true, "description": "🐳书链，一套代码适配H5、小程序、React Native", "packageManager": "pnpm@8.15.0", "scripts": {"postinstall": "node ./scripts/postinstall.mjs", "pnpm:devPreinstall": "echo 在安装依赖前统一pnpm和node版本 && node scripts/preinstall.mjs", "fix:mismatches": "echo 保持所有依赖版本统一，修改为最新的一个 && pnpm syncpack fix-mismatches", "fix:node18:openssl": "cross-env NODE_OPTIONS=--openssl-legacy-provider", "r": "echo reset，移除node_modules并安装依赖 && (pnpx rimraf node_modules || true) && pnpm i", "rm": "echo \"提交merge request\" && node rm.js", "mr": "echo \"提交merge request，不会随机@处理人\" && yunti mr", "mrr": "echo \"提交merge request并删除自己分支，不会随机@处理人\" && yunti mr -r", "up:api": "echo 更新API SDK && pnpm update @yunti-private/api-* --latest -r", "expo:check": "echo 用Expo检查react-native依赖，防止版本不兼容的情况 && pnpm expo install --check -- -w", "weapps:deploy": "echo 部署小程序，仅CI执行 && pnpm run fix:node18:openssl pnpm yunti deploy-weapps", "h5:deploy": "echo 部署h5，仅CI执行 && pnpm run fix:node18:openssl pnpm yunti deploy-apps", "bookln": "echo 开发书链web、rn，按w打开网页 && pnpm --filter @bookln-app/bookln-rn start", "bookln:c": "echo 开发书链web、rn（清除缓存），按w打开网页 && pnpm --filter @bookln-app/bookln start:clear", "bookln:web": "echo 开发书链web && pnpm --filter @bookln-app/bookln-rn web", "bookln:ios": "echo 开发书链iOS && (cd apps/bookln-rn && pnpm run ios)", "postbookln:pod": "sh ./scripts/postinstall.sh", "bookln:pod": "echo 安装书链iOS原生依赖 && (cd apps/bookln-rn && pnpm run pod)", "bookln:pod:c": "echo 清除并安装书链RN iOS原生依赖 && (cd apps/bookln-rn && pnpm run pod:c)", "bookln:android": "echo 开发书链Android && pnpm --filter @bookln-app/bookln-rn android", "bookln:xcode": "echo 用Xcode打开书链iOS原生工程 && pnpm --filter @bookln-app/bookln-rn xcode", "bookln:as": "echo 用Android Studio打开书链Android原生工程 && pnpm --filter @bookln-app/bookln-rn as", "bookln:dev:rn": "echo 开发书链RN，Windows、macOS可用 && pnpm --filter @bookln-app/bookln-rn start", "bookln:dev:rn:c": "echo 开发书链RN，清除缓存，Windows、macOS可用 && pnpm --filter @bookln-app/bookln-rn start:clear", "bookln:dev:rn:r": "echo 开发书链RN，删除node_modules，清除缓存，Windows、macOS可用 && pnpm r && pnpm --filter @bookln-app/bookln-rn start:clear", "bookln:dev:ios": "echo 开发书链RN，编译iOS原生代码并启动iOS模拟器，macOS可用，需安装Xcode && (cd apps/bookln-rn && pnpm run ios)", "bookln:dev:ios:init": "echo 如果提示baseInfo.json找不到就可以执行一下 && (cd apps/bookln-rn && pnpm run ios:init)", "bookln:dev:android": "echo 开发书链RN，编译Android原生代码并启动Android真机或模拟器，Windows、macOS可用，需配置Android开发环境 && pnpm --filter @bookln-app/bookln-rn android", "bookln:dev:build": "echo 书链RN开发包 && pnpm bookln:dev:build:ios && pnpm bookln:dev:build:android", "bookln:dev:build:ios": "echo \"书链RN iOS开发包\" && pnpx open-cli http://ci.jump.yeteam.com:8080/view/%E9%B2%B8%E5%92%95%E5%99%9C/job/bookln-ios-dev/build?token=BOOKLN_DEV_CLIENT", "bookln:dev:build:android": "echo \"书链RN Android开发包\" && pnpx open-cli http://ci.jump.yeteam.com:8080/view/%E9%B2%B8%E5%92%95%E5%99%9C/job/bookln-android-dev/build?token=BOOKLN_DEV_CLIENT", "bookln:deploy:ci": "pnpm run --filter bookln deploy:ci", "bookln:build:h5": "pnpm run fix:node18:openssl pnpm --filter bookln build:h5", "prebookln:build:ios": "sh ./scripts/postinstall.sh", "bookln:build:ios": "echo \"书链RN iOS测试包\" && pnpm --filter @bookln-app/bookln-rn build:ios", "prebookln:build:ios:dev": "sh ./scripts/postinstall.sh", "bookln:build:ios:dev": "echo \"书链RN iOS开发包\" && pnpm --filter @bookln-app/bookln-rn build:ios:dev", "bookln:build:ios:upload": "echo \"上传书链RN iOS测试包到 TestFlight\" && pnpm --filter @bookln-app/bookln-rn build:ios:upload", "bookln:build:android": "echo \"书链RN Android测试包\" && pnpm --filter @bookln-app/bookln-rn build:android", "bookln:build:android:dev": "echo \"书链RN Android开发包\" && pnpm --filter @bookln-app/bookln-rn build:android:dev", "bookln:build:android:release": "echo \"书链RN Android渠道包\" && pnpm --filter @bookln-app/bookln-rn build:android:release", "bookln:bundlejs:ios": "echo 使用 react native bundle 命令输出 ios 平台 js bundle 包 && pnpm --filter @bookln-app/bookln-rn bundlejs:ios", "bookln:bundlejs:android": "echo 使用 react native bundle 命令输出 android 平台 js bundle 包 && pnpm --filter @bookln-app/bookln-rn bundlejs:android", "syncpack:fix": "pnpm syncpack fix-mismatches", "check:unused:icon": "echo 检查 packages/icon/src/index.ts 中的是否有未用到 Icon && npx tsx scripts/analyze-unused-icons.ts", "check:unused:asIcon": "echo 检查 packages/icon/src/asIcon.ts 中的是否有未用到 asIcon && npx tsx scripts/analyze-unused-icons.ts -f packages/icon/src/asIcon.ts -p asIcon", "check:unused:arIcon": "echo 检查 packages/icon/src/arIcon.ts 中的是否有未用到 arIcon && npx tsx scripts/analyze-unused-icons.ts -f packages/icon/src/arIcon.ts -p arIcon", "check:unused:aiQAIcon": "echo 检查 packages/icon/src/aiQAIcon.ts 中的是否有未用到 aiQAIcon && npx tsx scripts/analyze-unused-icons.ts -f packages/icon/src/aiQAIcon.ts -p aiQAIcon", "check:unused:acIcon": "echo 检查 packages/icon/src/index.ts 中的是否有未用到的 AcIcon && npx tsx scripts/analyze-unused-icons.ts -f packages/icon/src/index.ts -p AcIcon", "update:im": "pnpm update @yunti-private/basic-im-impl @yunti-private/basic-im @yunti-private/basic-imds @yunti-private/connection @yunti-private/basic-im-dbutil-memo --latest -r"}, "devDependencies": {"@babel/cli": "7.23.0", "@babel/core": "7.23.2", "@babel/plugin-proposal-optional-chaining": "7.21.0", "@babel/plugin-transform-flow-strip-types": "7.26.5", "@babel/plugin-transform-optional-catch-binding": "7.24.1", "@babel/plugin-transform-optional-chaining": "7.23.4", "@babel/plugin-transform-private-methods": "7.25.9", "@babel/preset-env": "7.23.2", "@babel/preset-typescript": "7.24.1", "@biomejs/biome": "1.9.4", "@ctrl/tinycolor": "4.0.2", "@expo/config": "8.5.4", "@larksuiteoapi/node-sdk": "1.22.0", "@nx/devkit": "20.4.2", "@nx/eslint-plugin": "20.4.2", "@pmmmwh/react-refresh-webpack-plugin": "0.5.10", "@tailwindcss/line-clamp": "0.4.4", "@tanstack/eslint-plugin-query": "5.68.0", "@tarojs/cli": "3.6.15", "@tarojs/plugin-mini-ci": "3.6.26", "@tarojs/taro-loader": "3.6.15", "@tarojs/webpack5-runner": "3.6.15", "@testing-library/react": "15.0.7", "@types/ali-oss": "6.16.11", "@types/archiver": "6.0.2", "@types/crypto-js": "4.1.1", "@types/form-data": "2.5.0", "@types/howler": "2.2.9", "@types/jest": "29.5.12", "@types/marked": "5.0.0", "@types/node": "18.15.6", "@types/qrcode": "1.5.5", "@types/react": "18.2.14", "@types/react-dom": "18.0.10", "@types/react-native": "0.69.20", "@types/react-native-actionsheet": "2.4.3", "@types/react-native-canvas": "0.1.13", "@types/react-native-modalbox": "1.4.14", "@types/react-native-snap-carousel": "3.8.10", "@types/react-native-table-component": "1.2.8", "@types/react-syntax-highlighter": "15.5.13", "@types/redux-logger": "3.0.9", "@types/rn-fetch-blob": "1.2.7", "@types/uuid": "3.4.0", "@types/webpack-bundle-analyzer": "4.7.0", "@types/webpack-env": "1.18.0", "@types/xml2js": "0.4.14", "@typescript-eslint/eslint-plugin": "6.21.0", "@typescript-eslint/parser": "6.21.0", "@vitest/coverage-v8": "3.0.5", "@yunti-private/antd-tailwind-presets": "1.0.8", "@yunti-private/cli": "0.1.16", "ali-oss": "6.18.1", "autoprefixer": "10.4.16", "babel-jest": "29.7.0", "babel-loader": "8.2.1", "babel-plugin-import": "1.13.6", "babel-preset-taro": "3.6.15", "cache-loader": "4.1.0", "color-convert": "2.0.1", "cross-env": "7.0.3", "dingtalk-robot-sender": "1.2.0", "dotenv-flow": "4.1.0", "eslint": "8.46.0", "eslint-config-prettier": "9.1.0", "eslint-config-taro": "3.6.15", "eslint-plugin-import": "2.29.0", "eslint-plugin-react": "7.33.2", "eslint-plugin-react-hooks": "4.6.0", "expo-modules-autolinking": "1.5.1", "fs-extra": "11.2.0", "git-lab-cli": "2.0.7", "identity-obj-proxy": "3.0.0", "install-peerdeps": "3.0.3", "jest": "29.2.1", "jest-expo": "49.0.0", "less": "4.1.3", "less-loader": "11.1.0", "metro": "0.76.8", "miniprogram-ci": "2.1.9", "node-gyp-build": "4.6.0", "nx": "20.4.2", "open-cli": "7.2.0", "postcss": "8.4.23", "postcss-rem-to-responsive-pixel": "6.0.1", "prettier": "3.0.3", "prettier-plugin-tailwindcss": "0.5.6", "qrcode": "1.5.3", "react-native-calendars": "1.1305.0", "react-native-haptic-feedback": "2.2.0", "react-native-less-transformer": "1.4.0", "react-refresh": "0.11.0", "react-test-renderer": "18.2.0", "reactotron-react-native": "5.1.13", "redux-logger": "3.0.6", "rimraf": "4.4.1", "stylelint": "14.16.1", "syncpack": "12.3.3", "tailwind-merge": "2.1.0", "tailwindcss": "3.3.2", "taro-plugin-compiler-optimization": "1.0.4", "thread-loader": "4.0.1", "translation-check": "1.0.3", "ts-babel": "6.1.7", "ts-jest": "29.1.2", "ts-node": "10.9.1", "tsconfig-paths-webpack-plugin": "4.0.1", "tsx": "4.19.0", "typescript": "5.1.3", "weapp-tailwindcss": "3.7.0", "webpack": "5.69.0", "webpack-bundle-analyzer": "4.8.0", "xml2js": "0.6.2", "yargs": "17.7.2", "zx": "7.2.1"}, "dependencies": {"@antmjs/vantui": "3.2.1", "@babel/runtime": "7.21.0", "@bookln/math-exam": "workspace:*", "@bookln/permission": "workspace:*", "@config-plugins/react-native-blob-util": "6.0.0", "@config-plugins/react-native-pdf": "6.0.0", "@expo/react-native-action-sheet": "4.0.1", "@jgl/ai-qa": "workspace:*", "@jgl/ai-qa-v2": "workspace:*", "@jgl/biz-components": "workspace:*", "@jgl/biz-components-rojer-katex-mini": "workspace:*", "@jgl/biz-func": "workspace:*", "@jgl/biz-utils": "workspace:*", "@jgl/components": "workspace:*", "@jgl/container": "workspace:*", "@jgl/icon": "workspace:*", "@jgl/im": "workspace:*", "@jgl/logger": "workspace:*", "@jgl/ui-v4": "workspace:*", "@jgl/upload": "workspace:*", "@jgl/utils": "workspace:*", "@legendapp/list": "1.0.8", "@lodev09/react-native-exify": "0.2.7", "@react-native-async-storage/async-storage": "1.18.2", "@react-native-camera-roll/camera-roll": "7.0.0", "@react-native-clipboard/clipboard": "1.12.1", "@react-native-community/blur": "4.3.2", "@react-native-community/hooks": "3.0.0", "@react-native-community/netinfo": "11.4.1", "@react-native-voice/voice": "3.2.4", "@react-navigation/drawer": "6.6.14", "@react-navigation/native": "6.1.6", "@reduxjs/toolkit": "1.9.3", "@rneui/base": "4.0.0-rc.8", "@rneui/themed": "4.0.0-rc.8", "@rojer/katex-mini": "1.1.3", "@shopify/flash-list": "1.6.3", "@shopify/react-native-skia": "0.1.234", "@sleiv/react-native-graceful-exit": "0.1.0", "@tamagui/config": "1.78.0", "@tamagui/create-theme": "1.78.0", "@tamagui/lucide-icons": "1.78.0", "@tamagui/shorthands": "1.78.0", "@tamagui/themes": "1.78.0", "@tanstack/query-async-storage-persister": "5.71.10", "@tanstack/react-query": "5.68.0", "@tanstack/react-query-persist-client": "5.71.10", "@tarojs/components": "3.6.15", "@tarojs/components-rn": "3.6.15", "@tarojs/helper": "3.6.15", "@tarojs/plugin-framework-react": "3.6.15", "@tarojs/plugin-html": "3.6.15", "@tarojs/plugin-platform-alipay": "3.6.15", "@tarojs/plugin-platform-h5": "3.6.15", "@tarojs/plugin-platform-jd": "3.6.15", "@tarojs/plugin-platform-qq": "3.6.15", "@tarojs/plugin-platform-swan": "3.6.15", "@tarojs/plugin-platform-tt": "3.6.15", "@tarojs/plugin-platform-weapp": "3.6.15", "@tarojs/react": "3.6.15", "@tarojs/rn-runner": "3.6.15", "@tarojs/rn-supporter": "3.6.15", "@tarojs/runtime": "3.6.15", "@tarojs/runtime-rn": "3.6.15", "@tarojs/shared": "3.6.15", "@tarojs/taro": "3.6.15", "@tarojs/taro-rn": "3.6.15", "@types/lodash-webpack-plugin": "0.11.9", "@yunti-private/api-bizboot": "0.0.823", "@yunti-private/api-booklnboot": "0.0.696", "@yunti-private/api-gulu": "0.0.655", "@yunti-private/api-www": "0.0.1063", "@yunti-private/api-xingdeng-boot": "0.0.1230", "@yunti-private/basic-im": "1.0.46", "@yunti-private/basic-im-dbutil-memo": "1.0.19", "@yunti-private/basic-im-impl": "1.0.62", "@yunti-private/basic-imds": "1.0.21", "@yunti-private/connection": "1.2.10", "@yunti-private/env": "1.0.15", "@yunti-private/env-impl-rn": "1.0.1", "@yunti-private/env-impl-weapp": "1.0.4", "@yunti-private/env-impl-web": "1.0.18", "@yunti-private/jgl-ui": "0.1.1", "@yunti-private/logger-rn": "3.0.3", "@yunti-private/net": "1.0.27", "@yunti-private/net-impl-rn": "workspace:*", "@yunti-private/net-impl-weapp": "1.0.17", "@yunti-private/net-impl-web": "1.0.59", "@yunti-private/net-query-hooks": "0.2.3", "@yunti-private/net-sign-wasm": "0.1.14", "@yunti-private/platform-check": "1.0.1", "@yunti-private/rn-expo-updates-helper": "0.1.13", "@yunti-private/rn-iflytek": "0.1.6", "@yunti-private/rn-memory-logger": "1.0.6", "@yunti-private/storage": "1.0.1", "@yunti-private/utils-rn": "1.0.15", "@yunti-private/utils-taro": "1.0.15", "@yunti-private/utils-universal": "1.0.15", "@yunti-private/utils-web": "1.0.15", "@yunti/react-native-chivox": "https://github.com/yuntitech/react-native-chivox#405cbd3857de65d5bcbfd53d238734e3237c5277", "ahooks": "3.7.6", "archiver": "6.0.1", "axios": "1.5.1", "babel-plugin-lodash": "3.3.4", "classnames": "2.3.2", "copy-to-clipboard": "3.3.3", "crypto-js": "4.1.1", "dayjs": "1.11.7", "debug": "4.3.7", "deep-cleaner": "1.2.1", "dotenv": "16.3.1", "event-target-polyfill": "0.0.4", "expo": "49.0.21", "expo-av": "13.4.1", "expo-barcode-scanner": "12.7.0", "expo-camera": "13.6.0", "expo-constants": "14.4.2", "expo-dev-client": "2.4.12", "expo-font": "11.4.0", "expo-image": "1.8.1", "expo-image-manipulator": "11.6.0", "expo-image-picker": "14.5.0", "expo-linear-gradient": "12.5.0", "expo-linking": "5.0.2", "expo-localization": "14.3.0", "expo-router": "2.0.9", "expo-screen-orientation": "6.2.0", "expo-splash-screen": "0.20.5", "expo-status-bar": "1.6.0", "expo-system-ui": "2.4.0", "expo-updates": "0.18.19", "expo-web-browser": "12.3.2", "form-data": "4.0.0", "howler": "2.2.4", "inobounce": "0.2.1", "inversify-props": "2.2.6", "jotai": "2.2.1", "js-base64": "3.7.5", "lodash": "4.17.21", "lodash-webpack-plugin": "0.11.6", "lottie-miniprogram": "1.0.12", "lottie-react-native": "6.4.0", "lucide-react-native": "0.511.0", "mime": "4.0.1", "mitt": "3.0.1", "moment": "2.29.4", "native-wechat": "1.0.21", "nativewind": "2.0.11", "query-string": "7.1.1", "react": "18.2.0", "react-dom": "18.2.0", "react-markdown-math": "1.0.2", "react-native": "0.72.6", "react-native-actionsheet": "https://github.com/yuntitech/react-native-actionsheet.git#0c0346fcd7539dd73fa2f0096611e0af10eaa618", "react-native-anchor-point": "1.0.6", "react-native-animatable": "1.4.0", "react-native-audio-recorder-player": "3.6.12", "react-native-blob-util": "0.19.4", "react-native-canvas": "0.1.39", "react-native-collapsible-tab-view": "8.0.1", "react-native-device-info": "10.12.1", "react-native-drawer-layout": "3.3.0", "react-native-drop-shadow": "1.0.0", "react-native-gesture-handler": "2.13.4", "react-native-iap": "12.16.2", "react-native-image-colors": "2.3.0", "react-native-image-viewing": "0.2.2", "react-native-linear-gradient": "2.8.3", "react-native-markdown-display": "7.0.2", "react-native-marked": "6.0.7", "react-native-math-view": "3.9.5", "react-native-modal": "13.0.1", "react-native-modalbox": "2.0.2", "react-native-network-logger": "1.15.0", "react-native-pdf": "6.7.1", "react-native-permissions": "3.10.1", "react-native-qrcode-svg": "5.1.2", "react-native-reanimated": "3.6.1", "react-native-reanimated-table": "0.0.2", "react-native-render-html": "6.3.4", "react-native-root-siblings": "5.0.1", "react-native-root-toast": "3.5.1", "react-native-safe-area-context": "4.6.3", "react-native-scalable-image": "1.1.0", "react-native-screens": "software-mansion/react-native-screens#42d9c36a73b24136aebb44dba73a05adbb2e03dc", "react-native-select-dropdown": "3.4.0", "react-native-share": "10.0.2", "react-native-skia-shadow": "1.1.0", "react-native-snap-carousel": "3.9.1", "react-native-svg": "13.14.0", "react-native-swiper": "1.6.0", "react-native-toast-hybrid": "2.5.0", "react-native-url-polyfill": "2.0.0", "react-native-user-agent": "https://github.com/yuntitech/react-native-user-agent.git", "react-native-view-shot": "3.8.0", "react-native-web": "0.19.6", "react-native-webview": "13.6.3", "react-redux": "8.0.5", "react-syntax-highlighter": "15.6.1", "redux": "4.2.1", "reflect-metadata": "0.1.13", "rn-alioss": "0.2.5", "tamagui": "1.78.0", "taro-canvas": "0.0.3", "taro-ui": "https://github.com/yuntitech/package-taro-ui.git", "taro-virtual-list": "1.1.2", "use-debounce": "10.0.4", "uuid": "3.4.0", "wasm-dce": "1.0.2", "wasm-loader": "1.3.0", "weixin-js-sdk": "1.6.3", "weixin-js-sdk-ts": "1.6.1", "yet-another-abortcontroller-polyfill": "0.0.4", "zustand": "5.0.4"}, "peerDependencies": {"@yunti-private/rn-suntone": "0.0.8"}, "resolutions": {"core-js": "2.6.12", "markdown-it": "14.1.0"}, "pnpm": {"overrides": {"uuid": "3.4.0", "react-refresh": "0.14.0", "@yunti-private/env": "1.0.15", "@yunti-private/net-sign-wasm": "0.1.14"}, "patchedDependencies": {"expo-application@5.3.0": "patches/<EMAIL>", "rn-alioss@0.2.5": "patches/<EMAIL>", "react-native-canvas@0.1.39": "patches/<EMAIL>", "tamagui@1.78.0": "patches/<EMAIL>", "react-native-math-view@3.9.5": "patches/<EMAIL>"}}, "expo": {"install": {"//": ["说明：", "expo:check 命令要求 expo@49.0.13 依赖 react-native-gesture-handler@2.12.0", "但 iOS 只有 react-native-gesture-handler@2.13.4 才能编译过", "所以这里排除掉"], "exclude": ["react-native-gesture-handler"]}}}