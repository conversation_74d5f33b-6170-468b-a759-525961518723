import { router } from '@jgl/utils';
import { MemoryLogger } from '@yunti-private/rn-memory-logger';
import { useAtomValue } from 'jotai';
import { useCallback } from 'react';
import { withLoginLogicVersion } from '../../bookln-biz';
import { agreementStateAtom } from '../atom/agreementAtoms';
import { routerMap } from '../constants/routerMap';

/**
 * 协议检查 hook
 * 在执行特定方法前检查是否已同意协议
 */
export const useAgreementCheck = () => {
  const agreementState = useAtomValue(agreementStateAtom);

  /**
   * 包装需要检查协议的方法
   * @param fn 需要执行的方法
   * @returns 包装后的方法
   */
  const withAgreementCheck = useCallback(
    <T extends unknown[]>(fn?: (...args: T) => void) => {
      return (...args: T) => {
        try {
          if (agreementState === 'agreed') {
            // 已同意协议，直接执行方法
            fn?.(...args);
          } else {
            // 未同意协议，新逻辑跳转到登录页面，旧逻辑跳转到协议页面
            router.push(withLoginLogicVersion(routerMap.logIn, routerMap.agreement));
          }
        } catch (error) {
          MemoryLogger.log(`[useAgreementCheck] Error occurred: ${error?.toString()}`);
          // 如果发生错误，新逻辑跳转到登录页面，旧逻辑跳转到协议页面
          try {
            router.push(withLoginLogicVersion(routerMap.logIn, routerMap.agreement));
          } catch (routerError) {
            MemoryLogger.log(`[useAgreementCheck] Router error: ${routerError?.toString()}`);
          }
        }
      };
    },
    [agreementState],
  );

  return { withAgreementCheck };
};
