import type { Router } from '@jgl/utils';
import { router } from '@jgl/utils';
import { useCallback } from 'react';
import type { UseBizRouter } from './useBizRouterTypes';
import { useCheckLogIn } from './useCheckLogIn';
import { useCheckAgreement } from './useCheckAgreement';
// eslint-disable-next-line @nx/enforce-module-boundaries
import { withLoginLogicVersion } from '../../bookln-biz';

export const useBizRouter: UseBizRouter = () => {
  const { checkAgreement } = useCheckAgreement();
  const { checkLogIn } = useCheckLogIn();

  const pushOld = useCallback<Router['push']>(
    async (path, params) => {
      if (checkAgreement({ targetRoute: path })) {
        if (await checkLogIn({ route: path })) {
          router.push(path, params);
        }
      }
    },
    [checkAgreement, checkLogIn],
  );

  const pushNew = useCallback<Router['push']>(async (path, params) => {
    router.push(path, params);
  }, []);

  return { ...router, push: withLoginLogicVersion(pushNew, pushOld) };
};
