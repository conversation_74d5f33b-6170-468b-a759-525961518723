import { agreementStateAtom, isWeChatInstalledAtom, isWechatInstalled } from '@jgl/biz-func';
import { useAtom, useAtomValue } from 'jotai';
import { useEffect } from 'react';
import { withLoginLogicVersion } from '../../bookln-biz';

/**
 * 微信是否安装
 */
export const useWeChatIsInstalled = () => {
  const [isWeChatInstalled, setIsWeChatInstalled] = useAtom(isWeChatInstalledAtom);
  const agreementState = useAtomValue(agreementStateAtom);
  useEffect(() => {
    withLoginLogicVersion(
      () => {
        isWechatInstalled().then((isInstalled) => {
          setIsWeChatInstalled(isInstalled);
        });
      },
      () => {
        if (agreementState === 'agreed') {
          isWechatInstalled().then((isInstalled) => {
            setIsWeChatInstalled(isInstalled);
          });
        }
      },
    )();
  }, [agreementState, setIsWeChatInstalled]);

  return isWeChatInstalled;
};
