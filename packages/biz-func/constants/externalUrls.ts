import { Platform } from 'react-native';

export const externalUrls = {
  /** 书链用户服务协议 */
  agreement: {
    name: '用户服务协议',
    url: 'https://yuntisysoss.bookln.cn/tech/app/htmls/bookln-terms-of-service.html',
  },

  /** 书链用户隐私政策 */
  privacy: {
    name: '用户隐私政策',
    url: 'https://yuntisysoss.bookln.cn/tech/app/htmls/bookln-privacy.html',
  },

  /** 书链儿童隐私保护协议 */
  privacyForChildren: {
    name: '儿童隐私保护协议',
    url: 'https://yuntisysoss.bookln.cn/tech/app/htmls/bookln-privacy-for-kids.html',
  },

  /** 个人信息清单 */
  personalInfoList: {
    name: '个人信息清单',
    url:
      Platform.OS === 'android'
        ? 'https://yuntisysoss.bookln.cn/tech/app/htmls/permission-android.html'
        : 'https://yuntisysoss.bookln.cn/tech/app/htmls/permission-ios.html',
  },

  /** 第三方共享信息清单 */
  sharedInfoList: {
    name: '第三方共享信息清单',
    url: 'https://yuntisysoss.bookln.cn/tech/app/htmls/bookln-sdks.html',
  },

  /** 域名信息备案管理系统 */
  icpLookUp: {
    name: '域名信息备案查询',
    url: 'https://beian.miit.gov.cn/#/home',
  },

  /** 投诉，飞书多维表格 */
  report: {
    name: '投诉',
    url: 'https://zibfgih0a9y.feishu.cn/share/base/form/shrcnxc0mrDRVKFD7aeJjY9S58e',
  },
};

/**
 * app用户协议相关链接
 */
export const agreementLinks = [
  externalUrls.agreement,
  externalUrls.privacy,
  externalUrls.privacyForChildren,
];
