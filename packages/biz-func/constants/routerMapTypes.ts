export type RouterMap = {
  index: string;
  chat: string;
  eliminate: string;
  link: string;
  wordCompletion: string;
  listeningEar: string;
  earTrain: string;
  chatPdf: string;
  essayReading: string;

  /** 调试选项页 */
  debug: string;

  sentenceFollow: string;
  wordBrowsing: string;
  // TODO: cenfeng - 后端返回字段，因为后端返回的是 wordBrowsing
  wordBrowser: string;
  channelVideo: string;
  dictation: string;
  webViewDemo: string;
  webViewDemoNormal: string;
  maze: string;
  wordCard: string;
  chineseReading: string;
  scanCode: string;
  wordFollow: string;
  webView: string;
  shootingWord: string;
  /**
   * 咕噜球兑换
   */
  guluConvert: string;
  /**
   * 我的小屋
   */
  wordHouse: string;
  wordTetris: string;
  /**
   * 书世界
   */
  bookWorld: string;
  orderDetail: string;
  confirmOrder: string;
  confirmSuccess: string;
  /**
   * 我的书架
   */
  myBookshelf: string;
  bookShelf: string;
  /**
   * 书籍详情
   */
  bookDetail: string;

  /**
   * 少儿书籍详情
   */
  kidsBookDetail: string;

  /**
   * 书籍在线阅读
   */
  bookReading: string;

  /**
   * 个人中心
   */
  personalCenter: string;

  /**帮助中心 */
  helpCenter: string;

  /**
   * 背包
   */
  myBackpack: string;

  /**
   * 勋章盒
   */
  medalBox: string;
  /**
   * 学习设置
   */
  learnSetting: string;

  /**
   * 我的订单
   */
  myOrders: string;
  order: string;

  bookWorldMaze: string;
  /**
   * 代付订单
   */
  helpPayOrder: string;
  /** 趣味学习浏览 */
  happyLearnPreview: string;
  /** 趣味学习-玩 */
  happyLearnPlay: string;
  /** 趣味学习-测验 */
  happyLearnQuiz: string;

  /** 更多场景 */
  moreScene: string;

  /** 轻松记 */
  propertyView: string;

  /** 下单vip */
  orderVip: string;
  /** 亲子互动 */
  happyLearnParentChild: string;
  /** 排行榜 */
  rank: string;
  /** 飞机大战 */
  aircraftWar: string;
  /** 拼单词 */
  spellWord: string;
  /** 叠箱子 */
  stackBox: string;
  /** 接水果 */
  fruitRain: string;
  /** 抓鱼 */
  catchFish: string;
  /** 口算 */
  quickCalc: string;
  /** 连线答题 */
  lineExam: string;
  /** 必读名著 */
  masterpiece: string;

  wordReading: string;
  /**
   * 生字听写
   */
  chineseDictation: string;

  /** 听音识字 */
  listenToSound: string;
  /** 成语接龙 */
  idiom: string;
  /** 拼句子 */
  spellSentence: string;

  /** 推箱子 */
  pushBox: string;

  /** 设置页，RN */
  settings: string;

  /** 登录modal页，RN */
  logInModal: string;

  logIn: string;

  /** 登录modal页，RN */
  logInByPhone: string;

  /** 协议页，RN */
  agreement: string;

  /** 关于页，RN */
  about: string;

  /** 注销账号 */
  deleteAccount: string;

  /** 翻译 */
  languageTranslate: string;

  /** 点读场景 */
  clickReading: string;

  /** 跟读场景 */
  followReading: string;

  /** 点击分类场景 */
  clickClassify: string;

  /** 在线听书场景 */
  onlineListening: string;

  /** 写句子场景 */
  writeSentence: string;

  /** 趣味答题场景 */
  funExam: string;

  /** 时钟 */
  clock: string;
  /** 刻度尺 */
  ruler: string;
  /** 场景分享详情页 */
  sceneShareDetail: string;
  /** 赠送会员 */
  giftToMembers: string;
  /** 数字怪兽 */
  numMonster: string;
  /** 数字巧算 */
  numRiddle: string;

  /** 找答案 */
  findAnswer: string;
  /** 分享获取vip天数 */
  shareGetVipDays: string;

  jglOrderVip: string;
  /** 拍照答疑 */
  photoQuestionAnswer: string;
  /**
   * 拍照写文/作文批改
   */
  photoWriteEssay: string;

  /**
   * 口算检查
   */
  oralArithmeticCheck: string;

  /**
   * 拍照记单词
   */
  photoWordMemory: string;

  /**
   * 拍照点读
   */
  photoPointRead: string;
  /**
   * 拍照赏析古诗文
   */
  ancientPoetryAppreciation: string;
  /**
   * 拍照背诵
   */
  photoRecitation: string;
  /**
   * 通用工具
   */
  universalTool: string;
  /**
   * 古文赏析
   */
  appreciateAncientArticles: string;
  /**
   * 商城
   */
  mall: string;
  /**
   * 商品详情
   */
  goodsDetail: string;
  /**
   * 手机号绑定
   */
  bindPhone: string;

  /**
   * AI问答拍照
   */
  aiQATakePhoto: string;

  /**
   * 首页
   */
  home: string;
};

export const scenePagesKey = 'scenePages';
export const businessPagesKey = 'businessPages';
