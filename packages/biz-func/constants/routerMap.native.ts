import { RouterMap } from './routerMapTypes';

export const routerMap: RouterMap = {
  index: '/index',
  chat: '/chat',
  eliminate: '/eliminate',
  link: '/link',
  wordCompletion: '/wordCompletion',
  listeningEar: '/listeningEar',
  earTrain: '/earTrain',
  chatPdf: '/chatPdf',
  essayReading: '/essayReading',
  debug: '/debug',
  sentenceFollow: '/sentenceFollow',
  wordBrowsing: '/wordBrowsing',
  wordBrowser: '/wordBrowsing',
  channelVideo: '/channelVideo',
  dictation: '/dictation',
  webViewDemo: '/webViewDemo',
  webViewDemoNormal: '/webViewDemoNormal',
  guluConvert: '/guluConvert',
  maze: '/maze',
  wordCard: '/wordCard',
  propertyView: '/propertyView',
  scanCode: '/scanCode',
  wordFollow: '/wordFollow',
  webView: '/webView',
  shootingWord: '/shootingWord',
  wordHouse: '/wordHouse',
  wordTetris: '/wordTetris',
  bookWorld: '/bookWorld',
  orderDetail: '/orderDetail',
  confirmOrder: '/confirmOrder',
  confirmSuccess: '/confirmSuccess',
  myBookshelf: '/myBookshelf',
  bookShelf: '/myBookshelf',
  bookDetail: '/bookDetail',
  kidsBookDetail: '/kidsBookDetail',
  bookReading: '/bookReading',
  personalCenter: '/personalCenter',
  helpCenter: '/helpCenter',
  myOrders: '/myOrders',
  order: '/myOrders',
  bookWorldMaze: '/bookWorldMaze',
  helpPayOrder: '/helpPayOrder',
  happyLearnPreview: '/happyLearnPreview',
  happyLearnPlay: '/happyLearnPlay',
  happyLearnQuiz: '/happyLearnQuiz',
  moreScene: '/moreScene',
  orderVip: '/orderVip',
  happyLearnParentChild: '/happyLearnParentChild',
  rank: '/rank',
  aircraftWar: '/aircraftWar',
  spellWord: '/spellWord',
  stackBox: '/stackBox',
  fruitRain: '/fruitRain',
  catchFish: '/catchFish',
  masterpiece: '/masterpiece',
  wordReading: '/wordReading',
  chineseDictation: '/chineseDictation',
  listenToSound: '/listenToSound',
  idiom: '/idiom',
  spellSentence: '/spellSentence',
  pushBox: '/pushBox',
  settings: '/settings',
  logIn: '/login',
  logInModal: '/logInModal',
  logInByPhone: '/logInByPhone',
  agreement: '/agreement',
  about: '/about',
  deleteAccount: '/deleteAccount',
  clickReading: '/clickReading',
  quickCalc: '/quickCalc',
  lineExam: '/lineExam',
  followReading: '/followReading',
  languageTranslate: '/languageTranslate',
  clickClassify: '/clickClassify',
  onlineListening: '/onlineListening',
  writeSentence: '/writeSentence',
  funExam: '/funExam',
  clock: '/clock',
  ruler: '/ruler',
  sceneShareDetail: '/sceneShareDetail',
  numMonster: '/numMonster',
  numRiddle: '/numRiddle',
  giftToMembers: '/giftToMembers',
  findAnswer: '/findAnswer',
  photoQuestionAnswer: '/photoQuestionAnswer',
  chineseReading: '/chineseReading',
  backpackDetail: '/backpackDetail',
  shareGetVipDays: '/shareGetVipDays',
  jglOrderVip: '/orderVip',
  /**
   * 拍照写文/作文批改
   */
  photoWriteEssay: '/photoWriteEssay',

  /**
   * 口算检查
   */
  oralArithmeticCheck: '/oralArithmeticCheck',
  /**
   * 拍照记单词
   */
  photoWordMemory: '/photoWordMemory',

  /**
   * 拍照点读
   */
  photoPointRead: '/photoPointRead',
  /**
   * 拍照赏析古诗文
   */
  ancientPoetryAppreciation: '/ancientPoetryAppreciation',
  /**
   * 拍照背诵
   */
  photoRecitation: '/photoRecitation',

  /**
   * 通用工具
   */
  universalTool: '/universalTool',

  /**
   * 分享得会员
   */
  shareGetVipDays: '/shareGetVipDays',

  /**
   * 手机号绑定
   */
  bindPhone: '/bindPhone',

  /**
   * 首页
   */
  home: '/home',
};
