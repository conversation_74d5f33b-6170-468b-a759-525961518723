import { store } from '@jgl/biz-func';
import { envVars } from '@jgl/utils';
import type { YTRequest } from '@yunti-private/net';
import { getHeaderAppToken } from '../utils/getHeaderAppToken';

export const syntheticAudio = (data: {
  msgId: number;
  order: number;
  txt: string;
  uid: string;
  uri?: string;
}): YTRequest<{ result: string }> => {
  const sessionId = store.getState().userInfo.sessionId;
  const appId = envVars.appId().toString();
  return {
    url: '/aiBookChatRecord/syntheticAudio.do',
    data,
    project: 'booklnboot',
    headers: {
      'app-id': appId,
      'app-p-token': getHeaderAppToken(sessionId) || '',
    },
  };
};
