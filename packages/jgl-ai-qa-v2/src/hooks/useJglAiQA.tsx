import { useSafeAreaInsets } from '@jgl/biz-func';
import { showToast } from '@jgl/utils';
import {
  EndType,
  MessageContentType,
  MessageFlow,
  type ISession,
  type ITextPayload,
} from '@yunti-private/basic-im';
import { useSet<PERSON>tom } from 'jotai';
import { useCallback, useState } from 'react';
import { jglAiQAChatMessageListBackToBottomBtnVisibleAtom } from '../atoms/jglAiQAAtoms';
import {
  booklnAIRobotBizId,
  type BooklnAIRobotBizIdValueType,
} from '../utils/constants';
import { useCurrentSession } from './useIMCoreLogic';
import { useJglAIChat } from './useJglAIChat';

export const useJglAiQA = (props: {
  robotBizId?: BooklnAIRobotBizIdValueType;
}) => {
  const { robotBizId = booklnAIRobotBizId.AI_QA } = props;
  const { currentSession } = useCurrentSession();
  const {
    feedBackModalVisible,
    feedBackSessionId,
    feedBackMessageId,
    targetSessionOutputting,
    messagesRef,
    isLoadingMore,
    canLoadingMore,
    setCurrentSessionId,
    setChatMessageListAutoScroll,
    setFeedBackModalVisible,
    createIMSession,
    stopAudioPlaying,
    onPressTakePhoto,
    onPressSend,
    onPressResendOutMessage,
    onPressResendInMessage,
    onPressCopyMessage,
    onPressPlayAIMessage,
    onPressDislike,
    onPressRegenerate,
    onDismissFeedbackModal,
    onLoadMoreOldMsgs,
    onStartVoiceRecording,
    onStopVoiceRecording,
  } = useJglAIChat({ robotBizId, sessionId: currentSession?.sessionId });

  const safeInsets = useSafeAreaInsets();

  const [historyMenuVisible, setHistoryMenuVisible] = useState(false);
  const setJglAiQAChatMessageListBackToBottomBtnVisible = useSetAtom(
    jglAiQAChatMessageListBackToBottomBtnVisibleAtom,
  );

  /**
   * 显示历史会话侧边栏
   */
  const handlePressShowHistorySideMenu = useCallback(() => {
    setHistoryMenuVisible(true);
  }, []);

  /**
   * 关闭历史会话侧边栏
   */
  const handleDismissHistorySideMenu = useCallback(() => {
    setHistoryMenuVisible(false);
  }, []);

  /**
   * 点击历史会话记录项
   * @param item 历史会话记录项数据
   */
  const handlePressHistoryItem = useCallback(
    async (session: ISession) => {
      if (targetSessionOutputting) {
        showToast({
          title: '正在输出中,请稍后再试',
        });
        return;
      }
      setHistoryMenuVisible(false);
      setCurrentSessionId(session.sessionId);
      setChatMessageListAutoScroll(true);
      setJglAiQAChatMessageListBackToBottomBtnVisible(false);
    },
    [
      setChatMessageListAutoScroll,
      setCurrentSessionId,
      setJglAiQAChatMessageListBackToBottomBtnVisible,
      targetSessionOutputting,
    ],
  );

  /**
   * 创建新会话
   * 如果当前没有消息,提示已在新会话中
   * 否则清空消息列表和会话状态
   */
  const handlePressCreateNewSession = useCallback(() => {
    if (currentSession?.sessionId && messagesRef.current.length === 1) {
      showToast({
        title: '已经在新对话中',
      });
    } else {
      // const hasMessageOutputting = messagesRef.current.find(
      //   (msg) =>
      //     msg.flow === MessageFlow.In &&
      //     msg.contentType === MessageContentType.Text &&
      //     (msg.payload as ITextPayload).end === EndType.NotEnd,
      // );
      const streamInTextMessage = messagesRef.current.filter(
        (msg) =>
          msg.flow === MessageFlow.In &&
          msg.contentType === MessageContentType.Text &&
          (msg.payload as ITextPayload).streamType,
      );
      let hasMessageOutputting = false;
      if (streamInTextMessage.length > 0) {
        hasMessageOutputting = !!streamInTextMessage.find((msg) => {
          return (msg.payload as ITextPayload).end === EndType.NotEnd;
        });
      }
      if (hasMessageOutputting) {
        showToast({
          title: '正在输出中,请稍后再试',
        });
        return;
      }

      createIMSession().then((session) => {
        stopAudioPlaying();
        setJglAiQAChatMessageListBackToBottomBtnVisible(false);
        setCurrentSessionId(session.sessionId);
      });
    }
  }, [
    createIMSession,
    currentSession?.sessionId,
    messagesRef,
    setCurrentSessionId,
    setJglAiQAChatMessageListBackToBottomBtnVisible,
    stopAudioPlaying,
  ]);

  return {
    feedBackModalVisible,
    setFeedBackModalVisible,
    historyMenuVisible,
    setHistoryMenuVisible,
    isLoadingMore,
    feedBackSessionId,
    feedBackMessageId,
    currentSession,
    canLoadingMore,
    safeInsets,
    onPressShowHistorySideMenu: handlePressShowHistorySideMenu,
    onDismissHistorySideMenu: handleDismissHistorySideMenu,
    onPressHistoryItem: handlePressHistoryItem,
    onPressTakePhoto,
    onPressSend,
    onPressResendOutMessage,
    onPressResendInMessage,
    onPressCopy: onPressCopyMessage,
    onPressPlayAIMessage,
    onPressDislike,
    onPressRegenerate,
    onDismissFeedbackModal,
    onPressCreateNewSession: handlePressCreateNewSession,
    onLoadMoreOldMsgs,
    onStartVoiceRecording,
    onStopVoiceRecording,
  };
};
