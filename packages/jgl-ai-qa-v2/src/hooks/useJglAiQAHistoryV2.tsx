import {
  type ITextPayload,
  MessageContentType,
  type ISession,
} from '@yunti-private/basic-im';
import dayjs from 'dayjs';
import { useCallback, useEffect, useState } from 'react';
import type { IMSessionEnhancedDTO } from '../dto/IMSession';
import {
  useFetchIMSessions,
  useIMRobotConf,
  useIMSessions,
} from './useIMCoreLogic';
import { booklnAIRobotBizId } from '../utils/constants';

export const useJglAiQAHistoryV2 = () => {
  const { sessions } = useIMSessions();
  const { fetchSessionListApiState: historyListApiState } = useFetchIMSessions({
    robotBizId: booklnAIRobotBizId.AI_QA,
  });
  const [historyList, setHistoryList] = useState<IMSessionEnhancedDTO[]>([]);
  const { robotConf } = useIMRobotConf();

  const filterEmptySessions = useCallback(
    (targetSessions: ISession[]) => {
      return targetSessions.filter((item) => {
        if (item.title === robotConf?.robotName) {
          return false;
        } else {
          return true;
        }
      });
    },
    [robotConf?.robotName],
  );

  useEffect(() => {
    if (sessions.length > 0) {
      console.log(
        '🚀🚀🚀🚀🚀🚀 - leejunhui ~ useEffect ~ filterEmptySessions - sessions:',
        sessions,
      );
      const filteredSessions = filterEmptySessions(sessions);
      console.log(
        '🚀🚀🚀🚀🚀🚀 - leejunhui ~ useEffect ~ filterEmptySessions - filteredSessions:',
        sessions,
      );
      // const filteredSessions = sessions;
      let todayGroupData: ISession[] = [];
      const otherGroupData: Record<string, ISession[]> = {};

      filteredSessions?.forEach((item) => {
        const itemDate = dayjs(
          item.lastMessage.updateTime || item.lastMessage.time,
        );
        const today = dayjs();

        if (itemDate.isSame(today, 'day')) {
          todayGroupData.push(item);
        } else if (itemDate.isSame(today, 'month')) {
          otherGroupData['本月' as const] = [
            ...(otherGroupData['本月' as const] || []),
            item,
          ];
        } else {
          const monthGroup = itemDate.format('YYYY年MM月');
          otherGroupData[monthGroup] = [
            ...(otherGroupData[monthGroup] || []),
            item,
          ];
        }
      });
      todayGroupData = todayGroupData.sort(
        (a, b) => b.updateTime - a.updateTime,
      );

      // 设置 historyList
      const enhancedList: IMSessionEnhancedDTO[] = [];
      // 1. 添加 \"今天\"
      if (todayGroupData.length > 0) {
        enhancedList.push({ type: 'title', name: '今天' });
        enhancedList.push(
          ...todayGroupData.map((item) => ({
            type: 'content' as const,
            data: item,
          })),
        );
      }

      // 2. 提取并添加 \"本月\"
      let thisMonthData = otherGroupData['本月' as const];
      if (thisMonthData) {
        thisMonthData = thisMonthData.sort(
          (a, b) => b.updateTime - a.updateTime,
        );
        enhancedList.push({ type: 'title', name: '本月' });
        enhancedList.push(
          ...thisMonthData.map((item) => ({
            type: 'content' as const,
            data: item,
          })),
        );
        // 从 otherGroupData 中移除 \"本月\"，以便后续排序
        delete otherGroupData['本月' as const];
      }

      // 3. 对剩余月份进行排序并添加
      const sortedMonthKeys = Object.keys(otherGroupData).sort((a, b) => {
        // 将 "YYYY年MM月" 格式转回日期对象进行比较
        const dateA = dayjs(a, 'YYYY年MM月');
        const dateB = dayjs(b, 'YYYY年MM月');
        // 降序排列
        return dateB.diff(dateA);
      });

      sortedMonthKeys?.forEach((key) => {
        enhancedList.push({ type: 'title', name: key });
        const sortedData = (otherGroupData[key] || []).sort(
          (a, b) => b.updateTime - a.updateTime,
        );
        enhancedList.push(
          ...sortedData.map((item) => ({
            type: 'content' as const,
            data: item,
          })),
        );
      });

      // 首次加载或刷新: 直接设置状态
      setHistoryList(enhancedList);
    }
  }, [filterEmptySessions, sessions]);

  return {
    historyList,
    historyListApiState,
  };
};
