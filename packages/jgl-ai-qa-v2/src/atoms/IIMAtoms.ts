import type { RobotConfDTO } from '@jgl/biz-func';
import { atom } from 'jotai';
import type { IMAudioConfDTO } from '../dto/IMAudioConfDTO';
import type { IMAgentConfDTO } from '../dto/IMAgentConfDTO';

/**
 * IM 登录状态
 */
export const imLoginStatusAtom = atom<'idle' | 'loggedIn' | 'logout'>('idle');

/**
 * 智能体配置
 */
export const imAgentConfAtom = atom<IMAgentConfDTO | undefined>(undefined);

/**
 * IM 机器人配置
 */
export const imRobotConfAtom = atom<RobotConfDTO | undefined>(undefined);

/**
 * 意图识别机器人配置
 */
export const imIntentRecognizeRobotConfAtom = atom<RobotConfDTO | undefined>(undefined);

/**
 * IM 音频配置
 */
export const imAudioConfAtom = atom<IMAudioConfDTO | undefined>(undefined);

/** 是否显示 AI 聊天浮窗 */
export const imFloatBallVisibleAtom = atom<boolean>(false);

/** 图书 AI 聊天会话 ID */
export const imBookAISessionIdAtom = atom<string | undefined>(undefined);

/** 消息列表是否在顶部 */
export const imMessageListAtTopAtom = atom<boolean>(true);
