import { Jgl<PERSON><PERSON><PERSON>, JglYStack } from '@jgl/ui-v4';
import type { ISession } from '@yunti-private/basic-im';
import { forwardRef, useImperativeHandle } from 'react';
import { useJglAiQA } from '../hooks/useJglAiQA';
import { JglAiQAChatMessageList } from './JglAiQAChatMessageList';
import { JglAiQAFeedbackBottomModal } from './JglAiQAFeedbackBottomModal';
import { JglAiQAInputToolBar } from './JglAiQAInputToolBar';
import { booklnAIRobotBizId } from '../utils/constants';

type Props = {
  renderHeader?: React.ReactElement;
};

export type JglAiQAComponentRef = {
  openChatSessionItem: (item: ISession) => void;
  createNewChatSession: () => void;
};

export const JglAiQAComponent = forwardRef<JglAiQAComponentRef, Props>(
  (props, ref) => {
    const { renderHeader } = props;
    const {
      feedBackModalVisible,
      onDismissFeedbackModal,
      isLoadingMore,
      canLoadingMore,
      safeInsets,
      feedBackSessionId,
      feedBackMessageId,
      currentSession,
      onPressHistoryItem,
      onPressTakePhoto,
      onPressSend,
      onPressResendInMessage,
      onPressResendOutMessage,
      onPressCopy,
      onPressPlayAIMessage,
      onPressDislike,
      onPressRegenerate,
      onPressCreateNewSession,
      onLoadMoreOldMsgs,
      onStartVoiceRecording,
      onStopVoiceRecording,
    } = useJglAiQA({ robotBizId: booklnAIRobotBizId.AI_QA });

    console.log(
      'leejunhui - 🔥🔥🔥🔥🔥🔥 - JglAiQAComponent - currentSession:',
      currentSession,
    );

    useImperativeHandle(ref, () => ({
      openChatSessionItem: (item: ISession) => {
        console.log('openChatSessionItem', item);
        onPressHistoryItem(item);
      },
      createNewChatSession: () => {
        onPressCreateNewSession();
      },
    }));

    return (
      <JglYStack w={'full'} flex={1} position='relative'>
        {currentSession ? (
          <JglYStack flex={1} w='full' zIndex={1} position='relative'>
            <JglAiQAChatMessageList
              sessionId={currentSession.sessionId}
              isLoadingMore={isLoadingMore}
              canLoadingMore={canLoadingMore}
              bottomSafeAreaHeight={safeInsets.bottom}
              onPressResendInMessage={onPressResendInMessage}
              onPressResendOutMessage={onPressResendOutMessage}
              onPressCopy={onPressCopy}
              onPressPlayAIMessage={onPressPlayAIMessage}
              onPressDislike={onPressDislike}
              onPressRegenerate={onPressRegenerate}
              onLoadMoreOldMsgs={onLoadMoreOldMsgs}
              onPressSendMessage={onPressSend}
              renderHeader={renderHeader}
            />

            <JglAiQAInputToolBar
              safeBottom={safeInsets.bottom}
              onPressTakePhoto={onPressTakePhoto}
              onPressSend={onPressSend}
              onStartVoiceRecording={onStartVoiceRecording}
              onStopVoiceRecording={onStopVoiceRecording}
            />
          </JglYStack>
        ) : (
          <JglYStack flex={1} ai='center' jc='center'>
            <JglSpinner isLoading={true} />
          </JglYStack>
        )}

        {feedBackModalVisible && feedBackSessionId && feedBackMessageId ? (
          <JglAiQAFeedbackBottomModal
            visible={feedBackModalVisible}
            onDismiss={onDismissFeedbackModal}
            feedBackSessionId={feedBackSessionId}
            feedBackMessageId={feedBackMessageId}
          />
        ) : null}
      </JglYStack>
    );
  },
);
