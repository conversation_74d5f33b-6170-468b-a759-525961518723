import {
  parseWidthHeightFromURL,
  useImagePreview,
  useWindowDimensions,
} from '@jgl/biz-func';
import { VoicePlay } from '@jgl/components';
import { aiQAIcon } from '@jgl/icon';
import {
  JglImage,
  JglText,
  JglTouchable,
  JglView,
  JglXStack,
  JglYStack,
} from '@jgl/ui-v4';
import {
  type ICustomPayload,
  type IImagePayload,
  type IImageTextPayload,
  type ITextPayload,
  MessageContentType,
  MessageFlow,
  MessageStatus,
} from '@yunti-private/basic-im';
import { memo, useCallback, useEffect, useMemo, useState } from 'react';
import { ActivityIndicator, Image } from 'react-native';
import type { MarkedStyles } from 'react-native-marked/src/theme/types';
import { useDebouncedCallback } from 'use-debounce';
import type {
  BookAiIMCustomMessagePayloadData,
  BookAiIMCustomMessagePayloadDesc,
  BookAiIMCustomMessagePayloadExtension,
} from '../dto/IMCommandIntentPayload';
import type { IMMessage } from '../store/IMMessage';
import { JglAiQALoadingBall } from './JglAiQALoadingBall';
import { CustomMarkdownRenderer } from './markdown/CustomMarkdownRenderer';
import { CustomTokenizer } from './markdown/CustomTokenizer';
import { Markdown } from './markdown/Markdown';

type Props = {
  /** AI 问答消息对象 */
  message: IMMessage;
  /** 是否是最后一个消息 */
  isLastMessage: boolean;
  /** 重发 AI 回答消息按钮点击事件 */
  onPressResendInMessage: (message: IMMessage) => void;
  /** 重发用户提问消息按钮点击事件 */
  onPressResendOutMessage: (param: { message: IMMessage }) => void;
  /** 复制按钮点击事件 */
  onPressCopy: (message: IMMessage) => void;
  /** 播放 AI 消息按钮点击事件 */
  onPressPlayAIMessage: (message: IMMessage) => void;
  /** 不喜欢按钮点击事件 */
  onPressDislike: (message: IMMessage) => void;
  /** 重新生成按钮点击事件 */
  onPressRegenerate: (message: IMMessage) => void;

  /** 点击了追问消息 */
  onPressSendMessage: (param: { textContent: string }) => void;
};

// type Nodes = Array<RichTextProps.Text | RichTextProps.HTMLElement> | string;

/**
 * <AUTHOR>
 * @description AI 问答聊天列表Item
 * @date 2025_04_23
 */
export const JglAiQAChatMessageListItem = memo((props: Props) => {
  const {
    message,
    isLastMessage,
    onPressResendInMessage,
    onPressResendOutMessage,
    onPressCopy,
    onPressPlayAIMessage,
    onPressDislike,
    onPressRegenerate,
    onPressSendMessage,
  } = props;
  const { flow } = message;

  const { showImagePreview, ImagePreviewerComponent } = useImagePreview();

  const onPressViewResourceImage = useCallback(() => {
    if (message.contentType === MessageContentType.ImageText) {
      const { images } = message.payload as IImageTextPayload;
      if (images.length === 0) {
        return;
      }
      const image = images[0];
      if (!image) {
        return;
      }
      const { url } = image;
      showImagePreview([url]);
    } else if (message.contentType === MessageContentType.Image) {
      const { url } = message.payload as IImagePayload;
      showImagePreview([url]);
    }
  }, [message.contentType, message.payload, showImagePreview]);

  const windowSize = useWindowDimensions();
  const imageMaxWidth = useMemo(() => {
    return windowSize.width * (260 / 375);
  }, [windowSize.width]);
  const imageMaxHeight = useMemo(() => {
    return windowSize.width * (260 / 375);
  }, [windowSize.width]);

  const getImageInfo = useCallback(
    async (src: string): Promise<{ width: number; height: number } | null> => {
      try {
        return new Promise((resolve) => {
          Image.getSize(src, (width, height) => {
            resolve({
              width,
              height,
            });
          });
        });
      } catch (error) {
        console.error('获取图片信息失败:', error);
        return null;
      }
    },
    [],
  );

  const [imageDimensions, setImageDimensions] = useState<{
    width: number | string;
    height: number | string;
  }>({
    width: 0,
    height: 0,
  });

  useEffect(() => {
    if (
      message.contentType === MessageContentType.Image &&
      message.payload &&
      'url' in message.payload
    ) {
      const { url } = message.payload as IImagePayload;
      const { width, height } = parseWidthHeightFromURL(url) || {};
      if (!width || !height) {
        getImageInfo(url).then((info) => {
          if (info) {
            const { width: imageWidth, height: imageHeight } = info;
            const aspectRatio = imageWidth / imageHeight;

            if (aspectRatio > 1) {
              setImageDimensions({
                width: imageMaxWidth,
                height: imageMaxWidth / aspectRatio,
              });
            } else {
              setImageDimensions({
                width: imageMaxHeight * aspectRatio,
                height: imageMaxHeight,
              });
            }
          }
        });
      } else {
        const aspectRatio = width / height;

        if (aspectRatio > 1) {
          setImageDimensions({
            width: imageMaxWidth,
            height: imageMaxWidth / aspectRatio,
          });
        } else {
          setImageDimensions({
            width: imageMaxHeight * aspectRatio,
            height: imageMaxHeight,
          });
        }
      }
    }
  }, [
    message.contentType,
    getImageInfo,
    imageMaxWidth,
    imageMaxHeight,
    message.payload,
  ]);

  useEffect(() => {
    if (
      message.contentType === MessageContentType.ImageText &&
      message.payload &&
      'images' in message.payload
    ) {
      const { images } = message.payload as IImageTextPayload;
      if (images.length === 0) {
        return;
      }
      const image = images[0];
      if (!image) {
        return;
      }
      const { url } = image;
      const { width, height } = parseWidthHeightFromURL(url) || {};
      if (!width || !height) {
        getImageInfo(url).then((info) => {
          if (info) {
            const { width: imageWidth, height: imageHeight } = info;
            const aspectRatio = imageWidth / imageHeight;

            if (aspectRatio > 1) {
              setImageDimensions({
                width: imageMaxWidth,
                height: imageMaxWidth / aspectRatio,
              });
            } else {
              setImageDimensions({
                width: imageMaxHeight * aspectRatio,
                height: imageMaxHeight,
              });
            }
          }
        });
      } else {
        const aspectRatio = width / height;

        if (aspectRatio > 1) {
          setImageDimensions({
            width: imageMaxWidth,
            height: imageMaxWidth / aspectRatio,
          });
        } else {
          setImageDimensions({
            width: imageMaxHeight * aspectRatio,
            height: imageMaxHeight,
          });
        }
      }
    }
  }, [
    message.contentType,
    getImageInfo,
    imageMaxWidth,
    imageMaxHeight,
    message.payload,
  ]);

  const renderOutMsgContent = useMemo(() => {
    if (message.contentType === MessageContentType.Text) {
      const { text } = message.payload as ITextPayload;
      return (
        <JglView
          ai='center'
          jc='center'
          bg='#4E76FF'
          borderRadius={16}
          py={12}
          px={12}
          maxW={'full'}
        >
          <JglText
            color='$background'
            fontSize={16}
            fontWeight='400'
            style={{
              whiteSpace: 'pre-line',
              overflowWrap: 'break-word',
              wordBreak: 'break-word',
            }}
          >
            {text}
          </JglText>
        </JglView>
      );
    }
    if (message.contentType === MessageContentType.Image) {
      const { url } = message.payload as IImagePayload;
      return (
        <JglTouchable onPress={onPressViewResourceImage}>
          <JglImage
            resizeMode='contain'
            source={url}
            borderRadius={16}
            w={imageDimensions.width}
            h={imageDimensions.height}
          />
        </JglTouchable>
      );
    }
    if (message.contentType === MessageContentType.ImageText) {
      const { text, images } = message.payload as IImageTextPayload;
      if (images.length === 0) {
        return null;
      }
      const image = images[0];
      if (!image) {
        return null;
      }
      const { url } = image;
      return (
        <JglYStack
          ai='flex-start'
          jc='center'
          bg='#4E76FF'
          borderRadius={16}
          py={12}
          px={12}
          space={12}
          maxW={'full'}
        >
          <JglTouchable onPress={onPressViewResourceImage}>
            <JglImage
              resizeMode='contain'
              source={url}
              borderRadius={16}
              w={imageDimensions.width}
              h={imageDimensions.height}
            />
          </JglTouchable>
          <JglText
            color='$background'
            fontSize={16}
            fontWeight='400'
            style={{
              whiteSpace: 'pre-line',
              overflowWrap: 'break-word',
              wordBreak: 'break-word',
            }}
          >
            {text}
          </JglText>
        </JglYStack>
      );
    }

    return null;
  }, [
    imageDimensions.height,
    imageDimensions.width,
    message.contentType,
    message.payload,
    onPressViewResourceImage,
  ]);

  const onPressRetryButton = useCallback(() => {
    if (message.flow === MessageFlow.In) {
      onPressResendInMessage(message);
    } else {
      onPressResendOutMessage({
        message,
      });
    }
  }, [message, onPressResendInMessage, onPressResendOutMessage]);

  const renderRetryButton = useMemo(() => {
    return (
      <JglTouchable
        w={20}
        h={20}
        ai='center'
        jc='center'
        onPress={onPressRetryButton}
        style={{
          minWidth: 20,
          minHeight: 20,
        }}
      >
        <JglImage source={aiQAIcon.qaError} w={20} h={20} />
      </JglTouchable>
    );
  }, [onPressRetryButton]);

  const renderOutMsgLeftAccessory = useMemo(() => {
    if (message.flow === MessageFlow.Out) {
      const { status } = message;
      switch (status) {
        case MessageStatus.Sending:
          return <ActivityIndicator size={20} />;
        case MessageStatus.Success:
          return null;
        case MessageStatus.Fail:
          return renderRetryButton;
        default:
          return null;
      }
    }
    return null;
  }, [message, renderRetryButton]);

  const handlePressRegenerate = useCallback(() => {
    onPressRegenerate(message);
  }, [message, onPressRegenerate]);

  const debouncePressRegenerate = useDebouncedCallback(
    () => {
      handlePressRegenerate();
    },
    300,
    { leading: true, trailing: false },
  );

  const handlePressCopy = useCallback(() => {
    onPressCopy(message);
  }, [message, onPressCopy]);

  const debouncePressCopy = useDebouncedCallback(
    () => {
      handlePressCopy();
    },
    300,
    { leading: true, trailing: false },
  );

  const handlePressPlayAIMessage = useCallback(() => {
    onPressPlayAIMessage(message);
  }, [message, onPressPlayAIMessage]);

  const debouncePressPlayAIMessage = useDebouncedCallback(
    () => {
      handlePressPlayAIMessage();
    },
    300,
    { leading: true, trailing: false },
  );

  const handlePressDislike = useCallback(() => {
    onPressDislike(message);
  }, [message, onPressDislike]);

  const debouncePressDislike = useDebouncedCallback(
    () => {
      handlePressDislike();
    },
    300,
    { leading: true, trailing: false },
  );

  const renderFinishedInMsgBottomPanel = useMemo(() => {
    if (message.flow !== MessageFlow.In) {
      return null;
    }

    return (
      <JglXStack w='full' jc='space-between' ai='center'>
        <JglXStack space={16} ai='center'>
          <JglTouchable
            w={34}
            h={34}
            ai='center'
            jc='center'
            borderRadius={10}
            bg={'#F1F4FF'}
            onPress={debouncePressCopy}
          >
            <JglImage
              source={require('../assets/images/ic_copy.png')}
              w={20}
              h={20}
            />
          </JglTouchable>
          <JglTouchable
            w={34}
            h={34}
            ai='center'
            jc='center'
            borderRadius={10}
            bg={'#F1F4FF'}
            onPress={debouncePressPlayAIMessage}
          >
            {message.audioLoading ? (
              <ActivityIndicator color='#4E76FF' size={16} />
            ) : (
              <VoicePlay
                size={[34, 34]}
                color={'transparentPurple'}
                play={!!message.audioPlaying}
                onclick={debouncePressPlayAIMessage}
              />
            )}
          </JglTouchable>
          <JglTouchable
            w={34}
            h={34}
            ai='center'
            jc='center'
            borderRadius={10}
            bg={'#F1F4FF'}
            onPress={debouncePressDislike}
          >
            <JglImage
              source={require('../assets/images/ic_dislike.png')}
              w={20}
              h={20}
            />
          </JglTouchable>
        </JglXStack>

        {isLastMessage ? (
          <JglTouchable
            w={34}
            h={34}
            ai='center'
            jc='center'
            borderRadius={10}
            bg={'#F1F4FF'}
            onPress={debouncePressRegenerate}
          >
            <JglImage
              source={require('../assets/images/ic_regenerate.png')}
              w={20}
              h={20}
            />
          </JglTouchable>
        ) : null}
      </JglXStack>
    );
  }, [
    debouncePressCopy,
    debouncePressDislike,
    debouncePressPlayAIMessage,
    debouncePressRegenerate,
    isLastMessage,
    message.audioLoading,
    message.audioPlaying,
    message.flow,
  ]);

  const handleImagePress = useCallback(
    (pressMode: string, url: string) => {},
    [],
  );

  const customMarkdownRenderer = useMemo(
    () => new CustomMarkdownRenderer(handleImagePress),
    [handleImagePress],
  );

  const customTokenizer = useMemo(() => new CustomTokenizer(), []);

  const renderInMsg = useMemo(() => {
    if (message.flow === MessageFlow.In) {
      if (message.contentType === MessageContentType.Text) {
        const { end, text } = message.payload as ITextPayload;

        if (!end) {
          if (message.receivingFailed) {
            return (
              <JglXStack space={8} ai='center'>
                <JglXStack bg='white' py={12} px={12} borderRadius={16}>
                  <JglText fontSize={16} fontWeight='400' color='$color12'>
                    加载失败，点击重试
                  </JglText>
                </JglXStack>
                {renderRetryButton}
              </JglXStack>
            );
          } else {
            if (text === '') {
              console.log(
                'JglAiQAChatMessageListItem - renderInMsg - 正在思考 请稍等 text:',
                text,
              );
              return (
                <JglXStack
                  space={6}
                  bg='white'
                  py={12}
                  ai='center'
                  px={12}
                  borderRadius={16}
                >
                  <JglText fontSize={16} fontWeight='400' color='$color12'>
                    正在思考 请稍等
                  </JglText>
                  {/* 动效 */}
                  <JglAiQALoadingBall />
                </JglXStack>
              );
            } else {
              return (
                <JglYStack space={12}>
                  <JglYStack
                    bg='white'
                    py={12}
                    px={12}
                    borderRadius={16}
                    space={12}
                    maxW={'full'}
                  >
                    <Markdown
                      value={text}
                      styles={customMarkedStyles}
                      renderer={customMarkdownRenderer}
                      tokenizer={customTokenizer}
                      // chatStatus={chatStatusRef.current}
                    />
                  </JglYStack>
                  {message.followAskMessages?.map((followAskMessage) => {
                    return (
                      <JglTouchable
                        jc='flex-start'
                        ai='center'
                        key={followAskMessage}
                        onPress={() =>
                          onPressSendMessage({ textContent: followAskMessage })
                        }
                      >
                        <JglText
                          key={followAskMessage}
                          borderRadius={12}
                          borderWidth={1}
                          px={8}
                          py={8}
                          borderColor={'#C6D4F9'}
                          fontSize={16}
                          fontWeight='400'
                          color='#151B37'
                        >
                          {followAskMessage}
                        </JglText>
                      </JglTouchable>
                    );
                  })}
                </JglYStack>
              );
            }
          }
        } else {
          if (message.followAskMessages) {
            return (
              <JglYStack space={12}>
                <JglYStack
                  bg='white'
                  py={12}
                  px={12}
                  borderRadius={16}
                  space={12}
                  maxW={'full'}
                >
                  <Markdown
                    value={text}
                    styles={customMarkedStyles}
                    renderer={customMarkdownRenderer}
                    tokenizer={customTokenizer}
                    // chatStatus={chatStatusRef.current}
                  />
                  <>
                    <JglView w='full' h={1} bg='$color3' />
                    {renderFinishedInMsgBottomPanel}
                  </>
                </JglYStack>
                {message.followAskMessages?.map((followAskMessage) => {
                  return (
                    <JglTouchable
                      jc='flex-start'
                      ai='center'
                      key={followAskMessage}
                      onPress={() =>
                        onPressSendMessage({ textContent: followAskMessage })
                      }
                    >
                      <JglText
                        key={followAskMessage}
                        borderRadius={12}
                        borderWidth={1}
                        px={8}
                        py={8}
                        borderColor={'#C6D4F9'}
                        fontSize={16}
                        fontWeight='400'
                        color='#151B37'
                      >
                        {followAskMessage}
                      </JglText>
                    </JglTouchable>
                  );
                })}
              </JglYStack>
            );
          } else {
            return (
              <JglYStack
                bg='white'
                pt={12}
                pb={12}
                px={12}
                borderRadius={16}
                space={12}
                maxW={'full'}
              >
                <Markdown
                  value={text}
                  styles={customMarkedStyles}
                  renderer={customMarkdownRenderer}
                  tokenizer={customTokenizer}
                  // chatStatus={chatStatusRef.current}
                />
                <>
                  <JglView w='full' h={1} bg='$color3' />
                  {renderFinishedInMsgBottomPanel}
                </>
              </JglYStack>
            );
          }
        }
      }
    }
    return null;
  }, [
    customMarkdownRenderer,
    customTokenizer,
    message,
    onPressSendMessage,
    renderFinishedInMsgBottomPanel,
    renderRetryButton,
  ]);

  const renderCustomInMessage = useMemo(() => {
    if (message.contentType === MessageContentType.Custom) {
      const payload = message.payload as ICustomPayload;
      const {
        data: dataStr,
        desc: decStr = '{"textContent": ""}',
        extension: extensionStr,
      } = payload;
      console.log(
        '🚀🚀🚀🚀🚀🚀 - leejunhui ~ renderCustomInMessage ~ decStr:',
        decStr,
      );
      const data = JSON.parse(dataStr) as BookAiIMCustomMessagePayloadData;
      const desc = JSON.parse(decStr) as BookAiIMCustomMessagePayloadDesc;
      const extension = JSON.parse(
        extensionStr,
      ) as BookAiIMCustomMessagePayloadExtension;
      console.log(
        '🚀🚀🚀🚀🚀🚀 - leejunhui ~ renderCustomInMessage ~ extension:',
        extension,
      );
      if (data.isIntentCommand) {
        switch (extension.actionStatus) {
          case 'waitingResponse': {
            return (
              <JglXStack space={4} py={2} w='full' ai='center' px={12}>
                <JglImage
                  source={require('../assets/images/ic_ai_waiting_for_response.gif')}
                  width={40}
                  h={8}
                />
              </JglXStack>
            );
          }
          case 'commandIntentRecognize': {
            return (
              <JglXStack space={2} py={2} w='full' ai='center' px={12}>
                <ActivityIndicator size={20} color='#B8C8FF' />
                <JglText fontSize={16} fontWeight='400' color='#8DA4EF' px={12}>
                  {desc.textContent}
                </JglText>
              </JglXStack>
            );
          }
          case 'commandIntentRecognizeFailed': {
            return (
              <JglXStack space={2} py={2} w='full' ai='center' px={12}>
                <JglText fontSize={16} fontWeight='400' color='$red8' px={12}>
                  {desc.textContent}
                </JglText>
              </JglXStack>
            );
          }
          case 'executingCommand': {
            return (
              <JglXStack space={2} py={2} w='full' ai='center' px={12}>
                <ActivityIndicator size={20} color='#B8C8FF' />
                <JglText fontSize={16} fontWeight='400' color='#8DA4EF'>
                  {desc.textContent}
                </JglText>
              </JglXStack>
            );
          }
          case 'commandExecuteSuccess': {
            return (
              <JglText fontSize={16} fontWeight='400' color='#8DA4EF' px={12}>
                {desc.textContent}
              </JglText>
            );
          }
          case 'commandExecuteFailed': {
            return (
              <JglText fontSize={16} fontWeight='400' color='$red8' px={12}>
                {desc.textContent}
              </JglText>
            );
          }
        }
      } else {
        return null;
      }
    } else {
      return null;
    }
  }, [message.contentType, message.payload]);

  if (flow === MessageFlow.In) {
    if (message.contentType === MessageContentType.Text) {
      return (
        <JglYStack py={6} space={12}>
          <JglXStack py={6} px={12} space={8} ai='center'>
            {renderInMsg}
          </JglXStack>
        </JglYStack>
      );
    } else if (message.contentType === MessageContentType.Custom) {
      return renderCustomInMessage;
    }
  }
  return (
    <>
      <JglXStack
        flexDirection='row-reverse'
        py={6}
        px={12}
        space={8}
        ai='center'
      >
        {renderOutMsgContent}
        {renderOutMsgLeftAccessory}
      </JglXStack>
      <ImagePreviewerComponent />
    </>
  );
});

const customMarkedStyles: MarkedStyles = {
  table: { marginVertical: 4 },
  li: { paddingVertical: 4 },
  h1: { fontSize: 28 },
  h2: { fontSize: 24 },
  h3: { fontSize: 20 },
  h4: { fontSize: 18 },
  blockquote: { marginVertical: 8 },
  paragraph: { paddingVertical: 6 },
};
