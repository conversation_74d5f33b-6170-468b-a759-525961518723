import type { RobotConfDTO } from '@jgl/biz-func';
import { container } from '@jgl/container';
import {
  EndType,
  type ICustomPayload,
  type ITextPayload,
  MessageContentType,
  MessageFlow,
  sessionUtil,
} from '@yunti-private/basic-im';
import { enableMapSet } from 'immer';
import { create } from 'zustand';
import { devtools } from 'zustand/middleware';
import { immer } from 'zustand/middleware/immer';
import { eventBus } from '../event/eventBus';
import { JglAiQAChatMessagePageSize } from '../utils/constants';
import type { IMMessage } from './IMMessage';
import { useIMSessionStore } from './IMSessionStore';

// 启用 MapSet 插件
enableMapSet();

/**
 * 消息状态接口
 */
interface IMMessageState {
  /**
   * 会话消息列表，key 为会话 ID，value 为消息列表
   */
  messages: Map<string, IMMessage[]>;
  /**
   * 获取会话消息 API 状态
   */
  fetchMessageListApiState: 'loading' | 'success' | 'error';
  /**
   * 获取更多旧消息 API 状态
   */
  fetchMoreOldMessageListApiState: 'loading' | 'success' | 'error';
  /**
   * 获取更多新消息 API 状态
   */
  fetchMoreNewMessageListApiState: 'loading' | 'success' | 'error';
}

/**
 * 消息操作接口
 */
interface IMMessageActions {
  /**
   * 设置会话消息列表
   */
  setMessages: (messages: Map<string, IMMessage[]>) => void;
  /**
   * 设置获取会话消息 API 状态
   */
  setFetchMessageListApiState: (state: 'loading' | 'success' | 'error') => void;
  /**
   * 设置获取更多旧消息 API 状态
   */
  setFetchMoreOldMessageListApiState: (state: 'loading' | 'success' | 'error') => void;
  /**
   * 设置获取更多新消息 API 状态
   */
  setFetchMoreNewMessageListApiState: (state: 'loading' | 'success' | 'error') => void;
  /**
   * 添加消息，如果消息 ID 已存在，则更新消息
   */
  appendMessage: (param: {
    sessionId: string;
    message: IMMessage;
    robotConf: RobotConfDTO | undefined;
  }) => void;
  /**
   * 批量添加消息，如果消息 ID 已存在，则更新消息
   */
  appendMessages: (param: {
    sessionId: string;
    messages: IMMessage[];
    robotConf: RobotConfDTO | undefined;
  }) => void;
  /**
   * 标记所有接收到的消息为音频播放结束状态
   */
  markAllReceivedMessagesAudioPlayingStatusFinished: (param: { sessionId: string }) => void;
  /**
   * 标记接收到的消息为音频播放状态
   */
  markReceivedMessageAudioPlayingStatusPlayingWithAudioSrc: (param: {
    sessionId: string;
    audioSrc: string;
  }) => void;
  /**
   * 标记未完成的消息为失败状态
   */
  markNotFinishedInMessageFailed: (param: { sessionId: string }) => void;
  /**
   * 标记所有未完成的消息为失败状态
   */
  markAllNotFinishedInMessageFailed: () => void;

  /**
   * 标记目标消息为音频加载状态
   */
  markTargetMessageAudioStartLoading: (param: {
    sessionId: string;
    messageId: number;
  }) => void;
  /**
   * 标记目标消息为音频加载状态
   */
  markTargetMessageAudioFinishLoading: (param: {
    sessionId: string;
    messageId: number;
    audioSrc?: string;
  }) => void;

  /**
   * 获取更老的消息列表
   */
  fetchMoreOldMessageListSuccess: (param: {
    sessionId: string;
    messages: IMMessage[];
  }) => void;

  /**
   * 更新目标消息的音频播放状态
   */
  updateTargetMessageAudioPlayingStatus: (param: {
    sessionId: string;
    msgId: number;
    audioPlaying: boolean;
  }) => void;

  /**
   * 删除目标消息
   */
  deleteTargetMessage: (param: { sessionId: string; id: string }) => void;

  /**
   * 同步目标自定义消息的 msgId
   */
  syncTargetCustomMessageMsgId: (param: {
    sessionId: string;
    newUserSendMessage: IMMessage;
  }) => void;
}

/**
 * 消息 Store
 */
export const useIMMessageStore = create<IMMessageState & IMMessageActions>()(
  devtools(
    immer((set) => ({
      // 初始化
      messages: new Map(),
      fetchMessageListApiState: 'loading' as const,
      fetchMoreOldMessageListApiState: 'success' as const,
      fetchMoreNewMessageListApiState: 'success' as const,

      setMessages: (messages: Map<string, IMMessage[]>) => set({ messages }),
      setFetchMessageListApiState: (state: 'loading' | 'success' | 'error') =>
        set({ fetchMessageListApiState: state as 'loading' | 'success' | 'error' }),
      setFetchMoreOldMessageListApiState: (state: 'loading' | 'success' | 'error') =>
        set({ fetchMoreOldMessageListApiState: state as 'loading' | 'success' | 'error' }),
      setFetchMoreNewMessageListApiState: (state: 'loading' | 'success' | 'error') =>
        set({ fetchMoreNewMessageListApiState: state as 'loading' | 'success' | 'error' }),
      appendMessage: async (param: {
        sessionId: string;
        message: IMMessage;
        robotConf: RobotConfDTO | undefined;
      }) => {
        const { sessionId, message, robotConf } = param;

        // 通过 sessionId 获取会话，判断会话标题是否还是机器人的昵称
        // 如果是，则需要调用 IM SDK 的 summarizeMessage
        const targetSession = useIMSessionStore
          .getState()
          .sessions.find((s) => s.sessionId === sessionId);

        // console.log('IMMessageStore - appendMessage - targetSession:', { targetSession });
        set((state) => {
          console.log('IMMessageStore - appendMessage - param:', { sessionId, message });

          const oldMessages = state.messages.get(sessionId) || [];
          console.log('IMMessageStore - appendMessage - oldMessages:', oldMessages);

          // 检查消息是否已存在
          const messageIndex = oldMessages.findIndex((item) => item.id === message.id);
          console.log('IMMessageStore - appendMessage - messageIndex:', messageIndex);

          // 根据是否存在消息决定更新方式
          const newMessages =
            messageIndex === -1
              ? [message, ...oldMessages]
              : [
                  ...oldMessages.slice(0, messageIndex),
                  message,
                  ...oldMessages.slice(messageIndex + 1),
                ];
          console.log('IMMessageStore - appendMessage - newMessages before sort:', newMessages);

          // 按 msgId 和时间排序
          newMessages.sort((a, b) => {
            if (a.msgId === b.msgId) {
              return a.time - b.time;
            }
            return a.msgId - b.msgId;
          });
          console.log('IMMessageStore - appendMessage - newMessages after sort:', newMessages);

          const filteredMessages = newMessages.filter((currentMessage, index, array) => {
            return !currentMessage.isDeleted && !currentMessage.isPlaceholder;
          });

          // 更新消息列表
          state.messages.set(sessionId, filteredMessages);
          console.log(
            'IMMessageStore - appendMessage - state.messages after update:',
            state.messages,
          );
        });

        // 判断 message 是否已经接收完成
        if (message.flow === MessageFlow.In && message.contentType === MessageContentType.Text) {
          const textPayload = message.payload as ITextPayload;
          if (textPayload.streamType && textPayload.end === EndType.NormalEnd) {
            if (targetSession && message.msgId !== 1) {
              const { title } = targetSession;
              if (title === robotConf?.robotName) {
                console.log('IMMessageStore - appendMessage - 发起 summarizeMessage 请求', {
                  sessionId,
                  robotUserId: robotConf?.robotId?.toString(),
                });
                const bizId = sessionUtil.bizIdFromSessionId(sessionId);
                const response = await container.im().sendRequest<{ title: string }>({
                  path: '/message/summarizeMessage.do',
                  bizData: {
                    bizId,
                    robotUserId: robotConf?.robotId?.toString(),
                    bizCode: 'summarizeMessage',
                  },
                });
                if (response.success && response.data?.title) {
                  container.im().setSessionName({
                    sessionId,
                    name: response.data.title,
                  });
                }
              }
              if (!message.followAskMessages) {
                eventBus.emit('messageNeedFollowAsk', {
                  sessionId,
                  message: { ...message },
                });
                // 从 imAgentConfAtom 中获取是否要发送开场白追问消息
                // const agentConf = getDefaultStore().get(imAgentConfAtom);
                // if (agentConf?.followAskSwitch) {
                //   const response = await sendFollowAskMsg({
                //     sessionId,
                //     robotUserId: robotConf?.robotId?.toString() ?? '',
                //     lastMsgId: message.msgId,
                //   });
                //   if (response?.followAskMessages) {
                //     const followAskMessages: string[] = JSON.parse(response.followAskMessages);
                //     message.followAskMessages = followAskMessages;
                //   }
                // }
              }
            }
          }
          if (!textPayload.streamType && message.msgId === 1 && !message.followAskMessages) {
            console.log('leejunhui - 🔥🔥🔥🔥🔥🔥 - guideMessageNeedFollowAsk 触发咯');

            eventBus.emit('guideMessageNeedFollowAsk', {
              sessionId,
              message: { ...message },
            });
          }
        }
      },
      appendMessages: async (param: {
        sessionId: string;
        messages: IMMessage[];
        robotConf: RobotConfDTO | undefined;
      }) => {
        const { sessionId, messages, robotConf } = param;

        // 通过 sessionId 获取会话，判断会话标题是否还是机器人的昵称
        // 如果是，则需要调用 IM SDK 的 summarizeMessage
        const targetSession = useIMSessionStore
          .getState()
          .sessions.find((s) => s.sessionId === sessionId);

        console.log('IMMessageStore - appendMessages - targetSession:', { targetSession });

        // 判断 message 是否已经接收完成
        const firstMessage = messages[0];
        if (
          firstMessage?.flow === MessageFlow.In &&
          firstMessage?.contentType === MessageContentType.Text
        ) {
          const textPayload = firstMessage.payload as ITextPayload;
          if (textPayload.end === EndType.NormalEnd) {
            if (targetSession) {
              const { title } = targetSession;
              if (title === robotConf?.robotName) {
                console.log('IMMessageStore - appendMessages - 发起 summarizeMessage 请求', {
                  sessionId,
                  robotUserId: robotConf?.robotId?.toString() ?? '',
                });
                const bizId = sessionUtil.bizIdFromSessionId(sessionId);
                const response = await container.im().sendRequest<{ title: string }>({
                  path: '/message/summarizeMessage.do',
                  bizData: {
                    bizId,
                    robotUserId: robotConf?.robotId?.toString() ?? '',
                    bizCode: 'summarizeMessage',
                  },
                });
                if (response.success && response.data?.title) {
                  container.im().setSessionName({
                    sessionId,
                    name: response.data.title,
                  });
                }
              }
            }
          }
        }

        set((state) => {
          console.log('IMMessageStore - appendMessages - param:', { sessionId, messages });

          const oldMessages = state.messages.get(sessionId) || [];
          console.log('IMMessageStore - appendMessages - oldMessages:', oldMessages);

          // 创建一个新的消息列表，包含所有新消息和旧消息
          const newMessages = [...oldMessages];

          // 遍历新消息，更新或添加到列表中
          messages.forEach((message) => {
            const existingIndex = newMessages.findIndex((m) => m.id === message.id);
            if (existingIndex !== -1) {
              newMessages[existingIndex] = message;
            } else {
              newMessages.push(message);
            }
          });

          // 按 msgId 和时间排序
          newMessages.sort((a, b) => {
            if (a.msgId === b.msgId) {
              return a.time - b.time;
            }
            return a.msgId - b.msgId;
          });
          console.log('IMMessageStore - appendMessages - newMessages after sort:', newMessages);

          const filteredMessages = newMessages.filter((currentMessage) => {
            return !currentMessage.isDeleted && !currentMessage.isPlaceholder;
          });

          // 更新消息列表
          state.messages.set(sessionId, filteredMessages);
          console.log(
            'IMMessageStore - appendMessages - state.messages after update:',
            state.messages,
          );
        });
      },
      markAllReceivedMessagesAudioPlayingStatusFinished: (param: { sessionId: string }) =>
        set((state) => {
          const { sessionId } = param;
          const messages = state.messages.get(sessionId) || [];

          messages.forEach((message) => {
            if (message.flow === MessageFlow.In) {
              message.audioPlaying = false;
            }
          });
        }),
      markReceivedMessageAudioPlayingStatusPlayingWithAudioSrc: (param: {
        sessionId: string;
        audioSrc: string;
      }) =>
        set((state) => {
          const { sessionId, audioSrc } = param;
          const messages = state.messages.get(sessionId) || [];

          console.log(
            'leejunhui - 🔥🔥🔥🔥🔥🔥 - markReceivedMessageAudioPlayingStatusPlayingWithAudioSrc 来了',
            { sessionId, audioSrc, messages },
          );

          messages.forEach((message) => {
            if (message.tmpData) {
              const tempData = JSON.parse(message.tmpData);
              if (tempData.audioUrlList) {
                const audioUrlList: string[] = tempData.audioUrlList;
                const messageAudioIsPlaying = audioUrlList.some((url) => url === audioSrc);
                if (messageAudioIsPlaying) {
                  message.audioPlaying = true;
                }
              }
            } else {
              if (message.audioSrc === audioSrc) {
                message.audioPlaying = true;
              } else {
                message.audioPlaying = false;
              }
            }
          });
        }),
      markNotFinishedInMessageFailed: (param: { sessionId: string }) =>
        set((state) => {
          const { sessionId } = param;
          const messages = state.messages.get(sessionId) || [];

          messages.forEach((message) => {
            if (
              message.flow === MessageFlow.In &&
              message.contentType === MessageContentType.Text &&
              (message.payload as ITextPayload).end === EndType.NotEnd
            ) {
              message.receivingFailed = true;
            }
          });
        }),
      markAllNotFinishedInMessageFailed: () =>
        set((state) => {
          state.messages.forEach((messages, sessionId) => {
            messages.forEach((message) => {
              if (
                message.flow === MessageFlow.In &&
                message.contentType === MessageContentType.Text &&
                (message.payload as ITextPayload).end === EndType.NotEnd
              ) {
                message.receivingFailed = true;
              }
            });
          });
        }),
      markTargetMessageAudioStartLoading: (param: {
        sessionId: string;
        messageId: number;
      }) =>
        set((state) => {
          const { sessionId, messageId } = param;
          const messages = state.messages.get(sessionId) || [];

          messages.forEach((message) => {
            if (message.msgId === messageId) {
              message.audioLoading = true;
            }
          });
        }),
      markTargetMessageAudioFinishLoading: (param: {
        sessionId: string;
        messageId: number;
        audioSrc?: string;
      }) =>
        set((state) => {
          const { sessionId, messageId, audioSrc } = param;
          const messages = state.messages.get(sessionId) || [];

          messages.forEach((message) => {
            if (message.msgId === messageId) {
              message.audioLoading = false;
              message.audioSrc = audioSrc;
            }
          });
        }),
      fetchMoreOldMessageListSuccess: (param: {
        sessionId: string;
        messages: IMMessage[];
      }) => {
        set((state) => {
          const { sessionId, messages } = param;
          const oldMessageListMap = state.messages;

          const oldMsgListData = oldMessageListMap.get(sessionId);
          // 不能直接累加，需要做一波替换，如果本地和远端的消息 id 一样，就以远端的消息为准
          const updatedOldMsgListData = oldMsgListData?.map((oldMessage: IMMessage) => {
            const matchMessage = messages.find((newMessage: IMMessage) => {
              return oldMessage.id === newMessage.id;
            });
            if (matchMessage) {
              return matchMessage;
            } else {
              return oldMessage;
            }
          });
          // 再往 updatedOldMsgListData 中插入存在于 messageList 但是不存在于 oldMsgListData 的消息
          const newMessages = messages.filter((newMessage: IMMessage) => {
            const matchMessage = oldMsgListData?.find((oldMessage: IMMessage) => {
              return oldMessage.id === newMessage.id;
            });
            return !matchMessage;
          });
          const newMsgListData = [...(updatedOldMsgListData ?? []), ...newMessages];
          newMsgListData.sort((a, b) => {
            if (a.msgId === b.msgId) {
              return a.time - b.time;
            }
            return a.msgId - b.msgId;
          });

          oldMessageListMap.set(sessionId, [...newMsgListData]);
        });
      },
      updateTargetMessageAudioPlayingStatus: (param: {
        sessionId: string;
        msgId: number;
        audioPlaying: boolean;
      }) =>
        set((state) => {
          const { sessionId, msgId, audioPlaying } = param;
          const messages = state.messages.get(sessionId) || [];

          messages.forEach((message) => {
            if (message.msgId === msgId) {
              message.audioPlaying = audioPlaying;
            }
          });

          state.messages.set(sessionId, messages);
        }),
      deleteTargetMessage: (param: { sessionId: string; id: string }) =>
        set((state) => {
          const { sessionId, id } = param;
          const messages = state.messages.get(sessionId) || [];

          const newMessages = messages.filter((message) => {
            return message.id !== id;
          });

          state.messages.set(sessionId, newMessages);
        }),
      syncTargetCustomMessageMsgId: (param: {
        sessionId: string;
        newUserSendMessage: IMMessage;
      }) =>
        set((state) => {
          const { sessionId, newUserSendMessage } = param;
          const { id: messageId, msgId: newMsgId } = newUserSendMessage;
          const messages = state.messages.get(sessionId) || [];

          console.log('IMMessageStore - syncTargetCustomMessageMsgId - param:', {
            sessionId,
            newUserSendMessage,
          });
          console.log(
            'IMMessageStore - syncTargetCustomMessageMsgId - messages before update:',
            messages,
          );

          messages.forEach((message) => {
            if (
              message.flow === MessageFlow.In &&
              message.contentType === MessageContentType.Custom
            ) {
              const { extension } = message.payload as ICustomPayload;
              const { userSendMessageId } = JSON.parse(extension);
              console.log('IMMessageStore - syncTargetCustomMessageMsgId - checking message:', {
                messageId,
                userSendMessageId,
                match: userSendMessageId === messageId,
              });
              if (userSendMessageId === messageId) {
                message.msgId = newMsgId;
                message.time = newUserSendMessage.time + 1;
                console.log(
                  'IMMessageStore - syncTargetCustomMessageMsgId - updated message:',
                  message,
                );
              }
            }
          });

          messages.sort((a, b) => {
            if (a.msgId === b.msgId) {
              return a.time - b.time;
            }
            return a.msgId - b.msgId;
          });

          console.log(
            'IMMessageStore - syncTargetCustomMessageMsgId - messages after sort:',
            messages,
          );

          state.messages.set(sessionId, messages);
          console.log(
            'IMMessageStore - syncTargetCustomMessageMsgId - state.messages after update:',
            state.messages,
          );
        }),
    })),
  ),
);

// 监听 currentSessionId 变化,自动获取并更新消息列表
useIMSessionStore.subscribe((state, prevState) => {
  // 只有当 currentSessionId 发生变化时才执行
  if (state.currentSessionId !== prevState.currentSessionId) {
    const sessionId = state.currentSessionId;
    if (!sessionId) return;

    console.log('leejunhui - 🔥🔥🔥🔥🔥🔥 - useIMSessionStore.subscribe ~ sessionId:', sessionId);

    // 使用立即执行函数来处理异步操作
    (async () => {
      try {
        useIMMessageStore.getState().setFetchMessageListApiState('loading');
        // 获取消息列表
        const response = await container.im().getLatestMessageList({
          sessionId: sessionId,
          pageSize: JglAiQAChatMessagePageSize,
        });

        if (response.success && response.data) {
          // 获取现有的消息 Map
          const messageStore = useIMMessageStore.getState();
          const currentMessages = messageStore.messages;

          // 创建新的消息 Map,保留其他 sessionId 的消息列表
          const newMessageMap = new Map(currentMessages);
          newMessageMap.set(sessionId, response.data);

          // 更新消息列表到 IMMessageStore
          messageStore.setMessages(newMessageMap);
          useIMMessageStore.getState().setFetchMessageListApiState('success');
          console.log(
            'leejunhui - 🔥🔥🔥🔥🔥🔥 - useIMSessionStore.subscribe ~ currentSessionFetchLatestMessagesSuccess',
          );

          eventBus.emit('currentSessionFetchLatestMessagesSuccess', {
            sessionId,
            messages: response.data,
          });
        }
      } catch (error) {
        useIMMessageStore.getState().setFetchMessageListApiState('error');
        console.error('Failed to fetch message list:', error);
      }
    })();
  }
});
