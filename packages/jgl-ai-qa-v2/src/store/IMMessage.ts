import type { IMessage } from '@yunti-private/basic-im';

export interface IMMessage extends IMessage {
  /**
   * 音频播放状态
   */
  audioPlaying?: boolean;

  /**
   * 音频加载状态
   */
  audioLoading?: boolean;

  /**
   * 音频源
   */
  audioSrc?: string;

  /**
   * msg flow 为 in 时且 payload 的 end 为 0 时取该值
   */
  receivingFailed?: boolean;

  /**
   * 跳过 AI 回答的提问消息是否被处理，默认为 false
   */
  skipAIAnswerHandled?: boolean;

  /**
   * 追问消息
   */
  followAskMessages?: string[];
}
