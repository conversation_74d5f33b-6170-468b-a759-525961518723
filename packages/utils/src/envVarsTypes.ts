import type { EnumAppId } from './appIdTypes';

/**
 * 环境变量的封装，方便使用，同时支持Taro、RN。
 *
 * 环境变量来源
 * - Taro：dev.js、prod.js文件
 * - RN：.env文件，参考apps/language-rn/.env
 */
export type EnvVars = {
  /**
   * 微信iOS、Android SDK使用的微信appId
   */
  weChatAppId: () => string;

  weChatAppSecret: () => string;
  /**
   * 小程序原始 ID
   */
  miniProgramRawID: () => string;

  /**
   * 应用英文名字，如：chinese、language
   */
  appName: () => string;

  /**
   * 给用户看到的名字，如：鲸咕噜AI学英语
   */
  appNameForUser: () => string;

  /**
   * 如：
   * - https://gulu-daily.bookln.cn
   * - https://gulu.bookln.cn
   */
  host: () => string;

  /**
   * h5 host
   */
  mpHost: () => string;

  mpProject: () => string;

  /**
   * 内部的appId，如鲸咕噜学英语是92
   */
  appId: () => EnumAppId;

  /**
   * ICP备案号
   */
  appIcp: () => string;

  /**
   * 开启网络请求log
   */
  enableNetworkConsoleLog: () => boolean;

  /**
   * 忽略掉RN的LogBox，LogBox.ignoreAllLogs
   *
   * 有时候调试会方便一点
   */
  rnIgnoreLogBox: () => boolean;

  /**
   * feature toggle，是否是第一次打正式包，打出来用于申请软著、资质，不会用于提交市场
   *
   * @deprecated use featureToggles
   */
  ftFirstBuild: () => boolean;

  /**
   * feature toggle，同步学 - 更多按钮是否隐藏
   *
   * @deprecated use featureToggles
   */
  ftMoreSceneHidden: () => boolean;

  /**
   * feature toggle，webview 是否要增加 vConsole 的 search
   *
   * @deprecated use featureToggles
   */
  ftWebViewVConsole: () => boolean;

  /**
   * mp的业务code，即/app/guluAiLearnLanguage
   */
  mpBizRoute: () => string;

  /**
   * mp的业务code，即guluAiLearnLanguage
   */
  mpBizCode: () => string;

  /**
   * js bundle 构建时间
   */
  appBuildTime?: () => string;

  /**
   * js bundle 构建分支
   */
  appBuildBranch?: () => string;

  /**
   * js bundle 构建 commit
   */
  appBuildCommit?: () => string;

  /**
   * App 原生包构建时间
   */
  appNativeBuildTime?: () => string;

  /**
   * App 原生包构建分支
   */
  appNativeBuildBranch?: () => string;

  /**
   * App 原生包构建 commit
   */
  appNativeBuildCommit?: () => string;

  /**
   * 是否是 RN 开发环境
   */
  rnDevelopment?: () => boolean;
  /**
   * 热更新服务端地址
   */
  appHotFixServerBaseUrl?: () => string;

  /**
   * 版本号
   */
  appVersion: () => string;

  /**
   * 版本id
   */
  appVersionId: () => string;

  /**
   * iOS appstore id
   */
  iosAppstoreId: () => string;

  /**
   * 虚拟货币
   */
  virtualCurrency: () => string;

  /**
   * 应用token
   */
  appToken: () => string;

  /**
   * 机器人业务code
   */
  imRobotBizCode: () => string;

  /**
   * 意图识别机器人业务code
   */
  imIntentRecognizeRobotBizCode: () => string;

  /**
   * 讯飞SDK的appId
   */
  iflyTekAppId: () => string;

  /**
   * IM 音频配置 Code
   */
  imAudioConfCode: () => string;

  /**
   * 登录逻辑版本 new为新逻辑，old为旧逻辑
   */
  loginLogicVersion: () => string;
};
