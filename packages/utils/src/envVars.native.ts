import type { EnvVars } from './envVarsTypes';
import { featureToggles } from './featureToggles';

export const envVars: EnvVars = {
  weChatAppId: () => String(process.env.EXPO_PUBLIC_WECHAT_APP_ID),
  weChatAppSecret: () => String(process.env.EXPO_PUBLIC_WECHAT_APP_SECRET),
  miniProgramRawID: () => String(process.env.EXPO_PUBLIC_MINIPROGRAM_RAW_ID),
  appName: () => String(process.env.EXPO_PUBLIC_APP_NAME),
  appNameForUser: () => String(process.env.EXPO_PUBLIC_APP_NAME_FOR_USER),
  appId: () => Number(process.env.EXPO_PUBLIC_APP_ID),
  appIcp: () => String(process.env.EXPO_PUBLIC_APP_ICP),
  host: () => String(process.env.EXPO_PUBLIC_HOST),
  appVersion: () => String(process.env.EXPO_PUBLIC_APP_VERSION),
  appVersionId: () => String(process.env.EXPO_PUBLIC_APP_VERSION_ID),
  mpHost: () => String(process.env.EXPO_PUBLIC_MP_HOST),
  mpProject: () => '',
  mpBizCode: () => String(process.env.EXPO_PUBLIC_MP_BIZ_CODE),
  mpBizRoute: () => String(process.env.EXPO_PUBLIC_MP_BIZ_ROUTE),
  enableNetworkConsoleLog: () => String(process.env.EXPO_PUBLIC_ENABLE_NETWORK_LOG) === 'true',
  rnIgnoreLogBox: () => String(process.env.EXPO_PUBLIC_RN_IGNORE_LOG_BOX) === 'true',
  appBuildTime: () => String(process.env.EXPO_PUBLIC_YT_BUILD_TIME),
  appBuildBranch: () => String(process.env.EXPO_PUBLIC_YT_BRANCH),
  appBuildCommit: () => String(process.env.EXPO_PUBLIC_YT_COMMIT),
  appNativeBuildTime: () => String(process.env.EXPO_PUBLIC_APP_NATIVE_BUILD_TIME),
  appNativeBuildBranch: () => String(process.env.EXPO_PUBLIC_APP_NATIVE_BUILD_BRANCH),
  appNativeBuildCommit: () => String(process.env.EXPO_PUBLIC_APP_NATIVE_BUILD_COMMIT),
  rnDevelopment: () => String(process.env.EXPO_PUBLIC_RN_DEVELOPMENT) === 'true',
  appHotFixServerBaseUrl: () => String(process.env.EXPO_PUBLIC_APP_HOT_FIX_SERVER_BASE_URL),
  iosAppstoreId: () => String(process.env.EXPO_PUBLIC_IOS_APPSTORE_ID),
  virtualCurrency: () => String(process.env.EXPO_PUBLIC_VIRTUAL_CURRENCY),
  appToken: () => String(process.env.EXPO_PUBLIC_APP_TOKEN),

  // deprecated. feature toggles
  ftFirstBuild: featureToggles.firstBuild,
  ftMoreSceneHidden: featureToggles.moreSceneHidden,
  ftWebViewVConsole: featureToggles.webViewVConsole,

  imRobotBizCode: () => String(process.env.EXPO_PUBLIC_IM_ROBOT_BIZ_CODE),
  imIntentRecognizeRobotBizCode: () =>
    String(process.env.EXPO_PUBLIC_IM_INTENT_RECOGNIZE_ROBOT_BIZ_CODE),

  iflyTekAppId: () => String(process.env.EXPO_PUBLIC_IFLYTEK_APP_ID),

  imAudioConfCode: () => String(process.env.EXPO_PUBLIC_IM_AUDIO_CONF_CODE),
  loginLogicVersion: () => String(process.env.EXPO_PUBLIC_LOGIN_LOGIC_VERSION),
};
