import { useIMLogin } from '@jgl/ai-qa-v2';
import { AppLoginType, logInUuidAtom } from '@jgl/biz-func';
import { useAtomValue } from 'jotai';
import { useCallback } from 'react';
import { sendLogInRequest } from '../util/BooklnLoginUtils';

/**
 * 使用 uuid 登录
 * @returns
 */
export const useLogInByUuidInternal = () => {
  const logInUuid = useAtomValue(logInUuidAtom);

  const { imLogin } = useIMLogin();
  const logInByUuidInternal = useCallback(async () => {
    const user = await sendLogInRequest({
      loginType: AppLoginType.Uuid,
      ttpId: logInUuid,
    });

    if (user?.userId) {
      console.log('leejunhui - 🔥🔥🔥🔥🔥🔥 - uuid 登录成功，开始登录 IM');
      await imLogin(user.userId.toString());
    }

    return user;
  }, [imLogin, logInUuid]);

  return { logInByUuidInternal };
};
