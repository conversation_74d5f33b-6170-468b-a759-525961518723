import { useIMLogin } from '@jgl/ai-qa-v2';
import {
  agreementStateAtom,
  AppLoginType,
  logInUuidAtom,
  routerMap,
  store,
  updateUserInfo,
  useCheckUtil,
  useShowWeChatFeatures,
} from '@jgl/biz-func';
import { container } from '@jgl/container';
import { router, setStorage, USERINFO } from '@jgl/utils';
import { showToast } from '@yunti-private/jgl-ui';
import type { YTRequest } from '@yunti-private/net';
import { getDefaultStore, useAtom } from 'jotai';
import { useCallback, useMemo, useState } from 'react';
import { Alert } from 'react-native';
import { useToast } from 'react-native-toast-hybrid';
import { useAppConfig } from '../../config/useAppConfig';
import { login, loginAndBindMobile, queryByWxLoginKey } from '../api/UserServiceApi';
import type { UsersApiLoginDTO } from '../dto/UsersApiLoginDTO';
import type { LoginAndBindMobileParams, UsersApiLoginParams } from '../type/ApiFetcherParams.type';
import { useLogInByUuidInternal } from './useLogInByUuidInternal';
import { useWechat } from './useWechat';

/**
 * 登录相关逻辑
 */
export const useLogin = () => {
  const showWeChatFeatures = useShowWeChatFeatures();
  // 是否正在微信登录
  const [isLoggingInByWeChat, setIsLoggingInByWeChat] = useState(false);
  // 是否已经阅读并同意用户协议
  const [isChecked, setIsChecked] = useState(false);
  // 是否正在进行手机号登录
  const [isLoggingInBySmsCode, setIsLoggingInBySmsCode] = useState(false);
  // 是否正在绑定手机号
  const [isBinding, setIsBinding] = useState(false);
  // 是否正在跳过绑定手机号
  const [isSkippingBind, setIsSkippingBind] = useState(false);
  // 用户协议状态
  const [agreementState, setAgreementState] = useAtom(agreementStateAtom);
  // 手机号校验工具
  const { checkPhoneNumber, checkSmsCode } = useCheckUtil();
  // 微信登录工具
  const { logInByUuidInternal } = useLogInByUuidInternal();
  // 登录配置
  const loginConfig = useAppConfig('BooklnLoginConfig');
  // 是否显示微信登录推荐标签
  const { isShowWechatRecommendTag = true } = JSON.parse(loginConfig) as {
    isShowWechatRecommendTag: boolean;
  };
  // 即时通讯登录工具
  const { imLogin } = useIMLogin();
  // 提示框
  const toast = useToast();
  // 微信登录工具
  const { initWechat, getWeChatInfo } = useWechat();

  /**
   * 发送登录请求
   * @param args
   * @returns
   */
  const sendLogInRequest = useCallback(
    async (fetcher: YTRequest<UsersApiLoginDTO>) => {
      const response = await container.net().fetch(fetcher);
      const { success, data, msg } = response;
      if (success && data) {
        const user = data;
        await setStorage(USERINFO, user);
        store.dispatch(updateUserInfo(user));
        await imLogin(data.userId?.toString() ?? '');
        setAgreementState('agreed');
        router.replace(routerMap.home);
      } else {
        showToast({ title: msg ?? '登录失败，请稍后再试' });
      }
    },
    [imLogin, setAgreementState],
  );

  /**
   * 微信登录
   */
  const onPressLoginByWeChat = useCallback(async () => {
    if (showWeChatFeatures) {
      // 初始化微信
      initWechat();
      setIsLoggingInByWeChat(true);
      const weChatInfo = await getWeChatInfo();
      const { ttpId, nick, sex, smallPhoto } = weChatInfo ?? {};
      if (ttpId) {
        const fetcher = queryByWxLoginKey({ loginKey: ttpId });
        const { success, data, msg } = await container.net().fetch(fetcher);
        if (success) {
          const { id, mobile } = data ?? {};
          if (id && mobile) {
            // 去登录
            setIsLoggingInByWeChat(true);
            await sendLogInRequest(
              login({
                loginType: AppLoginType.WeChat,
                ttpId,
                nick,
                sex,
                smallPhoto,
              }),
            );
          } else {
            // 去绑定手机号
            router.push(routerMap.bindPhone, { weChatInfoStr: JSON.stringify(weChatInfo) });
          }
        } else {
          showToast({ title: msg ?? '请求数据失败' });
        }
        setIsLoggingInByWeChat(false);
      }
    } else {
      showToast({ title: '请安装微信后再试' });
    }
  }, [getWeChatInfo, initWechat, sendLogInRequest, showWeChatFeatures]);

  const onCheckedChange = useCallback((checked?: boolean) => {
    if (checked === undefined) {
      setIsChecked((preCheck) => !preCheck);
    } else {
      setIsChecked(checked);
    }
  }, []);

  /**
   * 浏览模式登录
   */
  const onPressBrowseMode = useCallback(async () => {
    Alert.alert(
      '',
      '未同意协议仅可使用浏览模式',
      [
        {
          text: '取消',
          style: 'cancel',
        },
        {
          text: '进入浏览模式 ',
          onPress: async () => {
            toast.loading('');
            await logInByUuidInternal();
            setAgreementState('disagreed');
            toast.hide();
          },
          style: 'default',
        },
      ],
      { cancelable: false },
    );
  }, [logInByUuidInternal, setAgreementState, toast]);

  const isShowBrowseModeButton = useMemo(() => {
    // 当已经处于浏览模式下进入的登录页面不显示浏览模式按钮
    return agreementState !== 'disagreed';
  }, [agreementState]);

  const prePressLoginByWeChat = useCallback(() => {
    if (isChecked) {
      onPressLoginByWeChat();
    } else {
      showToast({ title: '请先阅读并同意用户协议' });
    }
  }, [isChecked, onPressLoginByWeChat]);

  const handleBindMobile = useCallback(
    async (args: LoginAndBindMobileParams) => {
      const { mobile, validCode } = args;
      const checkedPhoneNumber = checkPhoneNumber(mobile);
      if (checkedPhoneNumber) {
        const checkedSmsCode = checkSmsCode(validCode);
        if (checkedSmsCode) {
          setIsBinding(true);
          const { ttpId, nick, sex, smallPhoto } = args ?? {};
          await sendLogInRequest(
            loginAndBindMobile({
              mobile: checkedPhoneNumber,
              validCode: checkedSmsCode,
              ttpId: ttpId ?? '',
              nick: nick,
              sex: sex,
              smallPhoto: smallPhoto,
            }),
          );
          setIsBinding(false);
        }
      }
    },
    [checkPhoneNumber, checkSmsCode, sendLogInRequest],
  );

  /**
   * 跳过绑定手机号，直接微信登录
   */
  const handleSkipBindAndLoginByWeChat = useCallback(
    async (params: UsersApiLoginParams) => {
      setIsSkippingBind(true);
      await sendLogInRequest(login(params));
      setIsSkippingBind(false);
    },
    [sendLogInRequest],
  );

  const handleLoginByPhone = useCallback(
    async (args: { phoneNumber?: string; smsCode?: string }) => {
      const { phoneNumber, smsCode } = args;
      const checkedPhoneNumber = checkPhoneNumber(phoneNumber);
      if (checkedPhoneNumber) {
        const checkedSmsCode = checkSmsCode(smsCode);
        if (checkedSmsCode) {
          const ttpId = await getDefaultStore().get(logInUuidAtom);
          setIsLoggingInBySmsCode(true);
          await sendLogInRequest(
            login({
              loginType: AppLoginType.SmsCode,
              mobile: checkedPhoneNumber,
              validCode: checkedSmsCode,
              ttpId: ttpId,
            }),
          );
          setIsLoggingInBySmsCode(false);
        }
      }
    },
    [checkPhoneNumber, checkSmsCode, sendLogInRequest],
  );

  return {
    onPressLoginByWeChat: prePressLoginByWeChat,
    isLoggingInByWeChat,
    onCheckedChange,
    isShowBrowseModeButton,
    isShowWechatRecommendTag,
    handleBindMobile,
    handleLoginByPhone,
    isChecked,
    onPressBrowseMode,
    logInByUuid: logInByUuidInternal,
    isLoggingIn: isLoggingInBySmsCode,
    isBinding,
    handleSkipBindAndLoginByWeChat,
    isSkippingBind,
  };
};
