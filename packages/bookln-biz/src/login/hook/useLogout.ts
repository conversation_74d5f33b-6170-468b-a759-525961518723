import { useIMLogout } from '@jgl/ai-qa-v2';
import { isLoggingOutAtom, updateUserInfo, useAppDispatch } from '@jgl/biz-func';
import { container } from '@jgl/container';
import { useSetAtom } from 'jotai';
import { useCallback } from 'react';
import { logout } from '../api/UserServiceApi';

/**
 * 退出登录
 * @returns
 */
export const useLogout = () => {
  const setIsLoggingOut = useSetAtom(isLoggingOutAtom);

  const dispatch = useAppDispatch();

  const { imLogout } = useIMLogout();

  /**
   * 退出登录
   * 先退出登录再使用 uuid 登录
   */
  const handleLogout = useCallback(async () => {
    setIsLoggingOut(true);
    await container.net().fetch(logout());
    // 退出登录 IM
    await imLogout();
    // 清除tid
    dispatch(updateUserInfo({}));

    setIsLoggingOut(false);
  }, [dispatch, imLogout, setIsLoggingOut]);

  return {
    logout: handleLogout,
  };
};
