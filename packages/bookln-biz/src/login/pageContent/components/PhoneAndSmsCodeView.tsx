import { ContentContainer } from '@jgl/components';
import { JglGameButton, JglText, JglXStack } from '@jgl/ui-v4';
import { showToast } from '@yunti-private/jgl-ui';
import { useCallback } from 'react';
import { View } from 'react-native';
import { Button, Input, ScrollView, Spinner, XStack, YStack } from 'tamagui';
import { usePhoneAndSmsCodeView } from '../../hook/usePhoneAndSmsCodeView';
import type { PhoneAndSmsCodeViewBizProps } from '../../type/PhoneAndSmsCodeView.type';
import { LoginAgreement } from './LoginAgreement';

/**
 * 登录、绑定手机号共用UI。包含
 * - 输入手机号
 * - 验证码
 * - 确认按钮
 * - 同意协议选项
 */
export const PhoneAndSmsCodeView = (props: PhoneAndSmsCodeViewBizProps) => {
  const {
    confirmButtonTitle,
    isConfirmButtonLoading,
    showAgreementView,
    onPressConfirm,
  } = props;
  const {
    isSmsCodeButtonDisabled,
    isSmsCodeButtonLoading,
    onChangePhoneNumber,
    onChangeSmsCode,
    onPressGetSmsCode,
    phoneNumber,
    smsCode,
    smsCodeButtonTitle,
    smsCodeInputRef,
    onCheckedChange,
    isChecked,
  } = usePhoneAndSmsCodeView();

  const handlePressConfirm = useCallback(() => {
    if (showAgreementView && !isChecked) {
      showToast({ title: '请先阅读并同意用户协议' });
    } else {
      onPressConfirm({ phoneNumber, smsCode });
    }
  }, [isChecked, onPressConfirm, phoneNumber, showAgreementView, smsCode]);

  return (
    <ScrollView className='flex-1 bg-white' keyboardShouldPersistTaps='handled'>
      <ContentContainer containerClassName='self-center'>
        <YStack space={'$3'} className='py-10' px={20}>
          <Input
            className='flex-1'
            autoFocus
            placeholder='请输入手机号'
            size={'$5'}
            keyboardType='phone-pad'
            value={phoneNumber}
            onChangeText={onChangePhoneNumber}
            autoComplete='off'
          />

          <XStack className='items-center' space={'$2'}>
            <Input
              ref={smsCodeInputRef}
              className='flex-1'
              placeholder='请输入验证码'
              size={'$5'}
              keyboardType='number-pad'
              value={smsCode}
              onChangeText={onChangeSmsCode}
              returnKeyType='go'
              autoComplete='off'
            />
            <View className='w-28 items-center justify-center'>
              {isSmsCodeButtonLoading ? (
                <Spinner color='#3E63DD' />
              ) : (
                <Button
                  chromeless
                  className='rounded-full'
                  size={'$4'}
                  color='#3E63DD'
                  onPress={onPressGetSmsCode}
                  disabled={isSmsCodeButtonDisabled}
                >
                  {smsCodeButtonTitle}
                </Button>
              )}
            </View>
          </XStack>

          {showAgreementView ? (
            <LoginAgreement
              textClassName='my-2'
              isChecked={isChecked}
              onCheckedChange={onCheckedChange}
            />
          ) : null}

          <JglGameButton
            radius={10}
            backgroundColor='#4E76FF'
            secondaryBgColor='#2E58E9'
            fontSize={16}
            fontWeight='bold'
            loading={isConfirmButtonLoading}
            onPress={handlePressConfirm}
          >
            <JglXStack minH={44} py={6} alignItems='center'>
              {isConfirmButtonLoading ? (
                <Spinner color='#fff' />
              ) : (
                <JglText fontSize={16} fontWeight='bold' color='white'>
                  {confirmButtonTitle}
                </JglText>
              )}
            </JglXStack>
          </JglGameButton>
        </YStack>
      </ContentContainer>
    </ScrollView>
  );
};
