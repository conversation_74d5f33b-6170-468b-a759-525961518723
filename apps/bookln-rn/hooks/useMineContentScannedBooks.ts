import { useAppSelector, useIsUuidUser } from '@jgl/biz-func';
import { container } from '@jgl/container';
import { router, useDidShow } from '@jgl/utils';
import { useAtomValue, useSetAtom } from 'jotai';
import { useCallback, useEffect } from 'react';
import { books } from '../api/UserBooksServiceApi';
import { bookShelfAtom } from '../atom';
import { routerMap } from '../utils/routerMap';

/**
 * 扫码看过的书
 */
export const useMineContentScannedBooks = () => {
  const bookShelf = useAtomValue(bookShelfAtom);

  const userId = useAppSelector((state) => state.userInfo.userId);
  const setBookShelf = useSetAtom(bookShelfAtom);
  const isUuidUser = useIsUuidUser();

  const fetchBookShelfData = useCallback(async () => {
    const response = await container.net().fetch(books());
    const { data, success } = response;
    if (success) {
      setBookShelf(data ?? []);
    }
  }, [setBookShelf]);

  useEffect(() => {
    if (userId && !isUuidUser) {
      fetchBookShelfData();
    }
  }, [fetchBookShelfData, isUuidUser, userId]);

  useDidShow(() => {
    fetchBookShelfData();
  });

  //   const router = useRouter();

  const onPressMore = useCallback(() => {
    router.push(routerMap.BookShelf);
  }, []);

  return {
    bookShelf,
    onPressMore,
    retry: fetchBookShelfData,
  };
};
