import { withLoginLogicVersion } from '@bookln/bookln-biz';
import {
  type UserInfo,
  agreementStateAtom,
  isLoadingUserInfoFromStorageAtom,
  updateUserInfo,
  useAppDispatch,
  useAppSelector,
} from '@jgl/biz-func';
import { USERINFO, storage } from '@jgl/utils';
import { useMount } from 'ahooks';
import { useAtom, useAtomValue } from 'jotai';
import { isNil } from 'lodash';
import { useCallback } from 'react';
import { useLogIn, useLogInByUuidInternal } from './useLogIn';
import { useProfile } from './useProfile';

const key = USERINFO;

/**
 * 启动时加载用户 + sessionId登录刷新用户信息
 */
export const useLoadAndRefreshUserInfo = () => {
  const [isLoadingUserInfoFromStorage, setIsLoadingUserInfoFromStorage] = useAtom(
    isLoadingUserInfoFromStorageAtom,
  );
  const { logInBySessionId } = useLogIn();

  const dispatch = useAppDispatch();
  const existingSessionId = useAppSelector((state) => state.userInfo.sessionId);
  const { profile } = useProfile();
  const agreementState = useAtomValue(agreementStateAtom);

  const { logInByUuidInternal: logInByUuid } = useLogInByUuidInternal();

  const loadAndRefreshUserInfo = useCallback(async () => {
    if (isNil(existingSessionId)) {
      setIsLoadingUserInfoFromStorage(true);
      const result = await storage.getItem(key);
      setIsLoadingUserInfoFromStorage(false);

      if (result) {
        try {
          const cachedUserInfo = JSON.parse(result) as UserInfo | undefined;
          const { sessionId: cachedSessionId } = cachedUserInfo ?? {};
          if (cachedUserInfo && cachedSessionId) {
            dispatch(updateUserInfo(cachedUserInfo));
            setIsLoadingUserInfoFromStorage(false);

            await logInBySessionId(cachedSessionId);
          }
        } catch (error) {}
      } else {
        if (agreementState === 'agreed') {
          // 如果 sessionId 不存在，且用户信息不存在，且同意过协议，则只会出现在修改过网络环境的情况下，这时执行一下 uuid 登录
          logInByUuid({ from: 'loadAndRefreshUserInfo' });
        }
      }
    }
  }, [
    existingSessionId,
    setIsLoadingUserInfoFromStorage,
    dispatch,
    logInBySessionId,
    agreementState,
    logInByUuid,
  ]);

  const loadAndRefreshUserInfoNew = useCallback(async () => {
    if (isNil(existingSessionId)) {
      setIsLoadingUserInfoFromStorage(true);
      const result = await storage.getItem(key);

      if (result) {
        try {
          const cachedUserInfo = JSON.parse(result) as UserInfo | undefined;
          const { sessionId: cachedSessionId } = cachedUserInfo ?? {};
          if (cachedUserInfo && cachedSessionId) {
            dispatch(updateUserInfo(cachedUserInfo));
            await logInBySessionId(cachedSessionId);
          }
        } catch (error) {}
      }
      setIsLoadingUserInfoFromStorage(false);
    }
  }, [existingSessionId, setIsLoadingUserInfoFromStorage, dispatch, logInBySessionId]);

  useMount(() => {
    withLoginLogicVersion(loadAndRefreshUserInfoNew, loadAndRefreshUserInfo)();
  });

  return { isLoadingUserInfoFromStorage };
};
