import { withAuthIdentity, withLogin } from '@bookln/bookln-biz';
import {
  PermissionEnum,
  PermissionHooks,
  PermissionPurposeScene,
} from '@bookln/permission';
import {
  agreementStateAtom,
  routerMap as bizRouterMap,
  useAgreementCheck,
  useNavigationBarHeight,
  useSafeAreaInsets,
} from '@jgl/biz-func';
import { envVars, router, useDidHide } from '@jgl/utils';
import type { ISession } from '@yunti-private/basic-im';
import { useAtomValue, useSetAtom } from 'jotai';
import { useCallback, useMemo, useRef, useState } from 'react';
import { Keyboard, type LayoutChangeEvent } from 'react-native';
import type { ICarouselInstance } from 'react-native-reanimated-carousel';
import { atomMap } from '../atom';
import {
  BooklnContent,
  type BooklnContentRef,
} from '../components/BooklnContent';
import type { ChatSessionListSideMenuRef } from '../components/ChatSessionListSideMenu';
import { MineContent } from '../components/MineContent';
import { HomeTab, ScanType } from '../types';
import { routerMap } from '../utils/routerMap';

export type HomeTabItem = {
  title: string;
  id: HomeTab;
  renderContent: (offsetTop?: number) => React.JSX.Element;
};

/**
 * 首页内容
 */
export const useHomeContent = () => {
  const { checkAndRequestPermission } = PermissionHooks.usePermission();

  const { withAgreementCheck } = useAgreementCheck();

  const setHomeTabIndex = useSetAtom(atomMap.homeTabIndexAtom);

  const { top } = useSafeAreaInsets();

  /**
   * 最小顶部标签容器高度
   */
  const miniTopTabContainerHeight = useMemo(() => {
    return top + 44;
  }, [top]);

  const carouselRef = useRef<ICarouselInstance>(null);

  const chatSessionListSideMenuRef = useRef<ChatSessionListSideMenuRef>(null);

  const booklnContentRef = useRef<BooklnContentRef>(null);

  const [topTabContainerHeight, setTopTabContainerHeight] = useState(
    miniTopTabContainerHeight,
  );

  const agreementState = useAtomValue(agreementStateAtom);

  /**
   * 标签列表
   */
  const tabList: HomeTabItem[] = useMemo(
    () => [
      {
        title: '书链555',
        id: HomeTab.Bookln,
        renderContent: (offsetTop?: number) => (
          <BooklnContent ref={booklnContentRef} offsetTop={offsetTop} />
        ),
      },
      {
        title: '我的',
        id: HomeTab.Mine,
        renderContent: (offsetTop?: number) => (
          <MineContent offsetTop={offsetTop} />
        ),
      },
    ],
    [],
  );

  /**
   * 扫描
   */
  const onPressScan = useCallback(() => {
    if (agreementState === 'agreed') {
      checkAndRequestPermission({
        permission: PermissionEnum.Camera,
        scene: PermissionPurposeScene.ScanSearch,
      }).then((result) => {
        if (result) {
          router.push(routerMap.ScanSearch, { scanType: ScanType.ScanParse });
        }
      });
    } else {
      router.push(bizRouterMap.agreement);
    }
  }, [agreementState, checkAndRequestPermission]);

  const navigationBarHeight = useNavigationBarHeight();

  const onPressShowChatSessionListSideMenu = useCallback(() => {
    Keyboard.dismiss();
    chatSessionListSideMenuRef.current?.openMenu();
  }, []);

  const onPressOpenChatSessionItem = useCallback((item: ISession) => {
    Keyboard.dismiss();
    booklnContentRef.current?.openChatSessionItem(item);
    chatSessionListSideMenuRef.current?.closeMenu();
  }, []);

  const onPressCreateNewChatSession = useCallback(() => {
    chatSessionListSideMenuRef.current?.closeMenu();

    booklnContentRef.current?.createNewChatSession();
  }, []);

  useDidHide(() => {
    Keyboard.dismiss();
  });

  const onLayoutTopTabContainer = useCallback((event: LayoutChangeEvent) => {
    const { height } = event.nativeEvent.layout;
    setTopTabContainerHeight(height);
  }, []);

  const onIndexChange = useCallback(
    (index: number) => {
      setHomeTabIndex(index);
    },
    [setHomeTabIndex],
  );

  return {
    onIndexChange,
    miniTopTabContainerHeight,
    tabList,
    onLayoutTopTabContainer,
    onPressScan:
      envVars.loginLogicVersion() === 'new'
        ? withAgreementCheck(onPressScan)
        : withAgreementCheck(withLogin(onPressScan)),
    topTabContainerHeight,
    carouseEnabled: agreementState === 'agreed',
    carouselRef,
    navigationBarHeight,
    chatSessionListSideMenuRef,
    onPressShowChatSessionListSideMenu:
      envVars.loginLogicVersion() === 'new'
        ? withAgreementCheck(onPressShowChatSessionListSideMenu)
        : withAuthIdentity(onPressShowChatSessionListSideMenu),
    onPressOpenChatSessionItem,
    onPressCreateNewChatSession:
      envVars.loginLogicVersion() === 'new'
        ? withAgreementCheck(onPressCreateNewChatSession)
        : withAuthIdentity(onPressCreateNewChatSession),
  };
};
