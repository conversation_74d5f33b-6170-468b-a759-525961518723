import { useAppSelector, useToggleAppVisible } from '@jgl/biz-func';
import { container } from '@jgl/container';
import { envVars } from '@jgl/utils';
import { captureEvent } from '@sentry/react-native';
import { appConfigApiGetAppConf } from '@yunti-private/api-xingdeng-boot';
import { Env } from '@yunti-private/env';
import {
  type ExpoUpdateConfig,
  type ExpoUpdateReleaseChannel,
  type ExpoUpdatesCheckOnLaunchType,
  expoUpdateHelper,
} from '@yunti-private/rn-expo-updates-helper';
import { MemoryLogger } from '@yunti-private/rn-memory-logger';
import * as Updates from 'expo-updates';
import { useAtom } from 'jotai';
import { useCallback, useEffect, useRef } from 'react';
import { Alert, Platform } from 'react-native';
import { getVersion } from 'react-native-device-info';
import type { RemoteUpdateConfDTO } from '../../api/dto';
import { atomMap } from '../../atom';
import type { ExpoUpdatesInfoOnLaunch } from '../../types';

export type UpdatePlatform = 'ios' | 'android';

// Define callback types for the core check function
type CheckAndUpdateCallbacks = {
  onChecking?: () => void; // For loading state start
  onChecked?: () => void; // For loading state end
  onNativeUpdateNeeded?: (latestRuntimeVersion: string) => void;
  onJSUpdateAvailable?: () => void; // Indicates check found an update
  onJSUpdateFetching?: () => void; // Fetch started
  onJSUpdateDownloaded?: (isNew: boolean) => void;
  onNoUpdateAvailable?: () => void;
  onError?: (error: Error) => void;
};

/**
 * <AUTHOR>
 * @date 2024/02/28
 * @description 热更新 hook
 */
export const useExpoUpdate = () => {
  const userInfo = useAppSelector((s) => s.userInfo);
  const [config, setConfig] = useAtom(atomMap.updateConfigAtom);
  const [updateInfoOnLaunch, setUpdateInfoOnLaunch] = useAtom(atomMap.updateInfoOnLaunch);
  const remoteUpdateConfRef = useRef<RemoteUpdateConfDTO>();
  const notUpdateJSBundleYetRef = useRef<boolean>(false);
  const initRef = useRef<boolean>(false);
  const isAlertShowingRef = useRef<boolean>(false);

  const eventListener = useCallback(
    (event: Updates.UpdateEvent) => {
      if (event.type === Updates.UpdateEventType.ERROR) {
        const { message } = event;
        captureEvent({
          message: `热更新报错 - ${message}`,
          level: 'error',
          tags: {
            category: 'expo-updates',
          },
          extra: {
            user_id: userInfo.userId,
            user_name: userInfo.name,
            env: container.env().env(),
            app_version: getVersion(),
            message,
          },
        });
      }
    },
    [userInfo.name, userInfo.userId],
  );

  Updates.useUpdateEvents(eventListener);

  useEffect(() => {
    if (config && !initRef.current) {
      initRef.current = true;
      const updateId = Updates.updateId;
      const runtimeVersion = Updates.runtimeVersion;
      const checkAutomatically = Updates.checkAutomatically;
      const isEmergencyLaunch = Updates.isEmergencyLaunch;
      const isEmbeddedLaunch = Updates.isEmbeddedLaunch;
      const expoUpdatesInfoOnLaunch: ExpoUpdatesInfoOnLaunch = {
        updateId,
        runtimeVersion,
        checkAutomatically,
        isEmergencyLaunch,
        isEmbeddedLaunch,
      };
      setUpdateInfoOnLaunch(expoUpdatesInfoOnLaunch);
    }
  }, [config, setUpdateInfoOnLaunch]);

  const handleNotUpdateYet = useCallback(() => {
    console.log('handleNotUpdateYet 点击了暂不更新，下次启动自动应用更新');
    notUpdateJSBundleYetRef.current = true;
  }, []);

  const handleUpdateRightNow = useCallback(async () => {
    await Updates.reloadAsync();
  }, []);

  const getLatestRuntimeVersion = useCallback(async (param: { platform: UpdatePlatform }) => {
    try {
      const baseUrl = envVars.appHotFixServerBaseUrl?.() || '';
      console.log('🚀🚀🚀🚀🚀🚀 - leejunhui ~ baseUrl:', baseUrl);
      const apiUrl = `${baseUrl}/api/appInfo/getLatest`;
      console.log('🚀🚀🚀🚀🚀🚀 - leejunhui ~ apiUrl:', apiUrl);
      const params = {
        appId: envVars.appId()?.toString(),
        platform: param.platform,
      };
      console.log('🚀🚀🚀🚀🚀🚀 - leejunhui ~ params:', JSON.stringify(params));
      const response = await fetch(apiUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(params),
      });
      const responseData = await response.json();
      if (responseData.success && responseData.data) {
        return responseData.data.runtimeVersion;
      } else {
        return undefined;
      }
    } catch (error) {
      console.error(
        `🐳 - useExpoUpdate - getLatestRuntimeVersion - 获取运行时版本失败: ${
          error instanceof Error ? error.message : String(error)
        }`,
      );
      return undefined;
    }
  }, []);

  // Core update checking logic
  const checkAndUpdate = useCallback(
    async (callbacks?: CheckAndUpdateCallbacks) => {
      callbacks?.onChecking?.();
      try {
        if (container.env().env() !== Env.Production) {
          // 非生产环境，才执行runtimeVersion检查
          MemoryLogger.log('runtimeVersion检查');
          // 1. Check Runtime Version
          const latestRuntimeVersion = await getLatestRuntimeVersion({
            platform: Platform.OS === 'ios' ? 'ios' : 'android',
          });

          if (!latestRuntimeVersion) {
            MemoryLogger.log('Failed to get latest runtimeVersion');
            throw new Error('Failed to get latest runtimeVersion');
          }

          if (!config?.runtimeVersion) {
            // Should not happen if config is loaded, but handle defensively
            MemoryLogger.log('Current runtimeVersion is unknow');
            throw new Error('Current runtimeVersion is unknown');
          }

          if (latestRuntimeVersion !== config.runtimeVersion) {
            MemoryLogger.log('onNativeUpdateNeeded');
            callbacks?.onNativeUpdateNeeded?.(latestRuntimeVersion);
            callbacks?.onChecked?.();
            return; // Stop here if native update needed
          }
        }

        // 2. Check JS Bundle Update
        const update = await Updates.checkForUpdateAsync();
        if (update.isAvailable) {
          MemoryLogger.log('onJSUpdateAvailable');
          callbacks?.onJSUpdateAvailable?.();
          callbacks?.onJSUpdateFetching?.();
          try {
            const fetchResult = await Updates.fetchUpdateAsync();
            callbacks?.onJSUpdateDownloaded?.(fetchResult.isNew);
            MemoryLogger.log('onJSUpdateDownloaded');
          } catch (fetchError: any) {
            console.error('Error fetching JS update:', fetchError);
            callbacks?.onError?.(
              fetchError instanceof Error ? fetchError : new Error(String(fetchError)),
            );
            MemoryLogger.log(`Error fetching JS update: ${fetchError}`);
          }
        } else {
          MemoryLogger.log('onNoUpdateAvailable');
          callbacks?.onNoUpdateAvailable?.();
        }
      } catch (error: any) {
        console.error('Error checking for updates:', error);
        MemoryLogger.log(`Error checking for updates: ${error}`);
        callbacks?.onError?.(error instanceof Error ? error : new Error(String(error)));
      } finally {
        console.error('onChecked');
        callbacks?.onChecked?.(); // Ensure loading state ends
      }
    },
    [config?.runtimeVersion, getLatestRuntimeVersion],
  );

  // Automatic check logic (uses checkAndUpdate with specific Alert handling)
  const checkUpdates = useCallback(
    async (updateConf: RemoteUpdateConfDTO, notUpdateJSBundleInCurrentLife: boolean) => {
      if (updateConf?.enabled && !notUpdateJSBundleInCurrentLife) {
        console.log('checkUpdates 开始执行 (自动)');

        // Check alert ref before starting
        if (isAlertShowingRef.current) {
          console.log('checkUpdates (自动) - Alert 正在显示，跳过本次检查');
          return;
        }

        await checkAndUpdate({
          onNativeUpdateNeeded: () => {
            if (isAlertShowingRef.current) return;
            isAlertShowingRef.current = true;
            Alert.alert('有新的原生包', '', [
              {
                text: '我知道了',
                onPress: () => {
                  notUpdateJSBundleYetRef.current = true;
                  isAlertShowingRef.current = false;
                },
              },
            ]);
          },
          onJSUpdateDownloaded: (isNew) => {
            if (isNew) {
              console.log('checkUpdates (自动) - fetchUpdateAsync() 有新版本');
              if (updateConf?.updateAlert || container.env().env() !== Env.Production) {
                if (isAlertShowingRef.current) return;
                isAlertShowingRef.current = true;
                Alert.alert('有新版本', '是否更新？', [
                  {
                    text: '暂不更新，下次启动自动应用更新',
                    style: 'cancel',
                    onPress: () => {
                      handleNotUpdateYet();
                      isAlertShowingRef.current = false;
                    },
                  },
                  {
                    text: '立即更新',
                    onPress: () => {
                      isAlertShowingRef.current = false;
                      handleUpdateRightNow();
                    },
                  },
                ]);
                console.log('checkUpdates (自动) - remoteUpdateConf?.updateAlert 提示用户进行更新');
              } else {
                // Silent update handled automatically by expo-updates on next launch
                console.log('checkUpdates (自动) - 静默更新下载完成');
              }
            }
          },
          onError: (error) => {
            // Maybe log to Sentry here? Automatic check errors are less critical to show user.
            console.error('自动检查更新失败:', error);
          },
        });
      } else {
        console.log(
          `checkUpdates (自动) - 不执行检查: enabled=${updateConf?.enabled}, notUpdateYet=${notUpdateJSBundleInCurrentLife}`,
        );
      }
    },
    [checkAndUpdate, handleNotUpdateYet, handleUpdateRightNow], // Added checkAndUpdate dependency
  );

  /**
   * 拉取远端配置项
   */
  const getRemoteUpdateConf = useCallback(async () => {
    const request = appConfigApiGetAppConf({
      confCode: 'bookln_app_hot_fix_strategy',
      // @ts-ignore
      confType: 'jgl',
    });
    const response = await container.net().fetch(request);
    if (response.success && response.data) {
      try {
        const remoteConf: RemoteUpdateConfDTO = JSON.parse(response.data);
        console.log('🚀 ~ getRemoteUpdateConf ~ remoteConf:', remoteConf);
        remoteUpdateConfRef.current = remoteConf;
        if (remoteConf.enabled) {
          checkUpdates(remoteConf, notUpdateJSBundleYetRef.current);
        }
      } catch (error) {
        console.log('getRemoteUpdateConf JSON.parse error:');
      }
    }
  }, [checkUpdates]);

  const appShowLockRef = useRef<boolean>(false);
  const lastCheckUpdateTimeRef = useRef<number>(0);

  const handleAppShow = useCallback(async () => {
    if (envVars.rnDevelopment?.()) {
      console.log('当前是开发环境，不执行热更新检查');
      return;
    }
    if (isAlertShowingRef.current) {
      console.log('handleAppShow - Alert 正在显示，跳过本次检查');
      return;
    }
    if (!appShowLockRef.current) {
      appShowLockRef.current = true;
      const currentDate = Date.now();
      if (
        lastCheckUpdateTimeRef.current === 0 ||
        (lastCheckUpdateTimeRef.current > 0 && currentDate - lastCheckUpdateTimeRef.current > 5000)
      ) {
        console.log(
          `handleAppShow 来了 -------------- getRemoteUpdateConf remoteUpdateConf-${remoteUpdateConfRef.current} currentDate - ${currentDate} lastCheckUpdateTimeRef.current -${lastCheckUpdateTimeRef.current}`,
        );
        if (!remoteUpdateConfRef.current) {
          // 如果远端配置为空，则尝试去拉取
          await getRemoteUpdateConf();
        } else {
          // 如果远端配置存在，并且热更新已开启，则尝试去检查新版本
          if (remoteUpdateConfRef.current?.enabled) {
            await checkUpdates(remoteUpdateConfRef.current, notUpdateJSBundleYetRef.current);
          }
        }
      } else {
        console.log(
          'handleAppShow 来了 --------------，但是需要忽略掉',
          currentDate,
          currentDate - lastCheckUpdateTimeRef.current,
        );
      }
      lastCheckUpdateTimeRef.current = currentDate;
      appShowLockRef.current = false;
    } else {
      console.log('handleAppShow - appShowLockRef locked');
    }
  }, [checkUpdates, getRemoteUpdateConf]);

  useToggleAppVisible({
    show: handleAppShow,
  });

  /**
   * 初始化热更新配置
   */
  const initUpdateConfig = useCallback(async () => {
    try {
      const content = await expoUpdateHelper.readExpoUpdateConfig();
      if (content) {
        const updateConfig: ExpoUpdateConfig = JSON.parse(content);
        setConfig(updateConfig);
        MemoryLogger.log('initUpdateConfig');
      } else {
        MemoryLogger.log('initUpdateConfig no content');
      }
    } catch (error) {
      // TODO: cenfeng - 是否需要埋点上报？
      MemoryLogger.log('initUpdateConfig failed');
    }
  }, [setConfig]);

  /**
   * 切换热更新渠道
   */
  const switchReleaseChannel = useCallback(
    async (selectedChannel: ExpoUpdateReleaseChannel) => {
      await expoUpdateHelper.switchReleaseChannel(selectedChannel);
      setConfig((prev) => {
        if (prev) {
          return {
            ...prev,
            releaseChannel: selectedChannel,
          };
        } else {
          return prev;
        }
      });
    },
    [setConfig],
  );

  /**
   * 切换热更新 checkOnLaunch
   */
  const switchCheckOnLaunch = useCallback(
    async (type: ExpoUpdatesCheckOnLaunchType) => {
      await expoUpdateHelper.switchCheckOnLaunch(type);
      setConfig((prev) => {
        if (prev) {
          return {
            ...prev,
            checkOnLaunch: type,
          };
        } else {
          return prev;
        }
      });
    },
    [setConfig],
  );

  /**
   * 更新热更新 launchWaitM
   * @param 启动时需要等待热更新检查的时间，单位毫秒
   */
  const updateLaunchWaitM = useCallback(
    async (time: number) => {
      await expoUpdateHelper.updateLaunchWaitM(time);
      setConfig((prev) => {
        if (prev) {
          return {
            ...prev,
            launchWaitM: time,
          };
        } else {
          return prev;
        }
      });
    },
    [setConfig],
  );

  // biome-ignore lint/correctness/useExhaustiveDependencies: <explanation>
  useEffect(() => {
    if (!config) {
      MemoryLogger.log('initUpdateConfig start');
      initUpdateConfig();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [config]);

  return {
    config,
    updateInfoOnLaunch,
    initUpdateConfig,
    switchReleaseChannel,
    switchCheckOnLaunch,
    updateLaunchWaitM,
    getLatestRuntimeVersion,
    manualCheckForUpdate: checkAndUpdate, // Expose the core logic function
  };
};

/**
 * 反复切换前后台，非 Production 环境，有新包，用户不操作，是否会弹多次 Alert？
结论： 不会。
原因：
当第一次检查发现更新并弹出 Alert 时，isAlertShowingRef.current 会被设置为 true。
在用户与这个 Alert 交互（点击按钮）之前，isAlertShowingRef.current 会一直保持 true。
当 App 再次从后台切换到前台时，handleAppShow 函数会被触发。
handleAppShow 函数一开始就会检查 isAlertShowingRef.current。由于它现在是 true，函数会直接 return，不会执行任何检查逻辑，也不会调用 checkUpdates 或 checkAndUpdate。
因此，只要第一个 Alert 没有被用户处理掉，后续的后台切换不会触发新的检查，也就不会弹出新的 Alert。
当前逻辑是否覆盖到了所有的场景？
结论： 当前逻辑覆盖了大部分核心和常见的场景，并且通过 isAlertShowingRef 和频率控制处理了并发和重复触发的问题。
已覆盖场景：
自动检查： App 从后台到前台触发检查（有频率和 Alert 状态控制）。
手动检查： Debug 页面按钮触发检查（使用不同的 UI 回调）。
原生包更新： 检测到 runtimeVersion 不匹配，提示原生更新。
JS Bundle 更新 (需要提示)： 在非生产环境或配置了 updateAlert 时，检测到 JS 更新并下载成功后，弹出选择框让用户选择是否立即更新。
JS Bundle 更新 (静默)： 在生产环境且未配置 updateAlert 时，检测到 JS 更新并下载成功后，不打扰用户，更新会在下次 App 重启时自动应用（这是 expo-updates 的默认行为）。
无可用更新： 检查后发现没有新版本。
用户选择“暂不更新”： 记录状态 (notUpdateJSBundleYetRef)，在当前 App 生命周期内不再触发自动检查的更新流程。
错误处理： checkAndUpdate 中对获取 runtimeVersion、checkForUpdateAsync、fetchUpdateAsync 的主要步骤都包含了 try/catch，并将错误传递给 onError 回调。自动检查的回调会打印错误，手动检查的回调会用 Alert 显示错误。
开发环境： handleAppShow 会跳过检查。
可能未覆盖或可优化的边缘场景 (相对次要)：
获取远程配置失败 (getRemoteUpdateConf)： 如果网络请求失败或 JSON.parse 失败，当前只打印了日志，没有明确的用户反馈或重试机制。可能需要根据业务需求决定是否需要更明显的处理。
Updates.reloadAsync() 失败： 如果用户点击“立即更新”后，reloadAsync 调用本身失败，App 不会重启。当前没有处理这种情况。
更细粒度的错误反馈： 当前手动检查的 onError 只显示了通用的错误消息。如果需要，可以根据错误类型提供更具体的提示（例如“网络错误，请稍后重试” vs “更新包下载失败”）。
磁盘空间不足： fetchUpdateAsync 可能会因为磁盘空间不足而失败，这会归入通用的 fetchError 处理。
总结：
该 Hook 的逻辑在防止重复弹窗和处理核心更新流程方面是比较健壮的。主要的场景都已覆盖。对于一些非常边缘的错误情况（如 reloadAsync 失败或远程配置接口完全不可用），可以根据实际需要考虑是否增加额外的处理逻辑。
 * 
 */
