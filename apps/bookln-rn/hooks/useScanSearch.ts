import { PermissionEnum, PermissionHooks, PermissionPurposeScene } from '@bookln/permission';
import { successVibrate } from '@jgl/biz-func';
import { router, useDidHide, useDidShow, useWindowDimensions } from '@jgl/utils';
import { showToast } from '@yunti-private/jgl-ui';
import { useDebounceFn } from 'ahooks';
import { Audio } from 'expo-av';
import * as BarcodeScanner from 'expo-barcode-scanner';
import { type BarCodeScanningResult, FlashMode } from 'expo-camera';
import * as ImagePicker from 'expo-image-picker';
import { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import {
  Easing,
  useAnimatedStyle,
  useSharedValue,
  withRepeat,
  withTiming,
} from 'react-native-reanimated';
import { manipulateAsync, SaveFormat } from 'expo-image-manipulator';
import { useToast } from 'react-native-toast-hybrid';
import type { CameraScannerRef } from '../components/CameraScanner';
import { stringIsUrl } from '../utils/QRUtil';
import { qrQuery, treatBarCode, treateCrCodeOfUrl } from '../utils/QRUtils';
import { parseCustomQueryItems } from '../utils/WebViewHelper';
import { SoundUrl } from '../utils/constants';
import { routerMap } from '../utils/routerMap';
import { Platform } from 'react-native';

// 最大扫描框大小
const maxBoxSize = 327;

type CornerPoint = {
  rightTop: { x: number; y: number };
  rightBottom: { x: number; y: number };
  leftBottom: { x: number; y: number };
  leftTop: { x: number; y: number };
};

/**
 * 二维码信息
 */
export type QRCodeInfo = {
  data: string;
  type: string;
  cornerPoint?: CornerPoint;
};

/**
 * 扫码搜索
 */
export const useScanSearch = () => {
  const cameraScannerRef = useRef<CameraScannerRef>(null);

  const toast = useToast();

  const [flashMode, setFlashMode] = useState<FlashMode>(FlashMode.off);

  const [cameraEnabled, setCameraEnabled] = useState(true);

  const { checkAndRequestPermission } = PermissionHooks.usePermission();

  const [scanned, setScanned] = useState(false);

  const [reviewPicChoiceQrConfig, setReviewPicChoiceQrConfig] = useState<{
    imgUrl: string;
    qrCodes: QRCodeInfo[];
  }>();

  // const params = useRouterParams<{ scanType: ScanType }>();

  const { width, height } = useWindowDimensions();

  const centerX = useMemo(() => width / 2, [width]);

  const centerY = useMemo(() => height / 2, [height]);

  const boxSize = useMemo(() => {
    return Math.min(maxBoxSize, Math.min(width, height) - 48);
  }, [width, height]);

  // 创建动画值
  const scanLinePosition = useSharedValue(0);

  // const { scanType: defaultScanType = ScanType.ScanParse } = params;

  // /**
  //  * 扫码类型
  //  */
  // const [scanType, setScanType] = useState<ScanType>(defaultScanType);

  /**
   * 查询二维码
   */
  const queryQRCode = useCallback(
    async (code: string) => {
      await qrQuery({
        crCode: code,
        source: 'scanQrCode',
        isFromQr: true,
      }).finally(() => {
        toast.hide();
      });
    },
    [toast],
  );

  const { run: runQueryQRCode } = useDebounceFn(queryQRCode, {
    wait: 1000,
  });

  const dealScanResult = useCallback(
    async (result: QRCodeInfo) => {
      const { data, type } = result;
      const isBarCode = BarcodeScanner.Constants.BarCodeType.ean13 === type;
      const looksLikeBarCode = !stringIsUrl(data) && (data || '').length === 13;
      if (isBarCode || looksLikeBarCode) {
        // 条形码 相册读取条形码 没有type 故判断位数13且不是url
        await treatBarCode(data);
        toast.hide();
      } else {
        if (stringIsUrl(data)) {
          router.push(routerMap.WebView, { url: data });
          toast.hide();
          return;
        }
        const isWeChatUrl =
          data?.includes('http://weixin.qq.com/') || data?.includes('https://weixin.qq.com/');
        if (stringIsUrl(data) && !isWeChatUrl) {
          // 其他
          const parameters = parseCustomQueryItems(data);

          if (parameters != null && !Object.prototype.hasOwnProperty.call(parameters, 'crcode')) {
            treateCrCodeOfUrl(data);
            toast.hide();
            return;
          }
        }
        runQueryQRCode(data);
      }
    },
    [runQueryQRCode, toast],
  );

  /**
   * 播放音效
   */
  const playBeepSound = useCallback(async () => {
    try {
      const { sound } = await Audio.Sound.createAsync({
        uri: SoundUrl.SCAN_SUCCESS_SOUND,
      });
      await sound.playAsync();
    } catch (error) {
      console.log('播放音效出错:', error);
    }
  }, []);

  // 用来存储收集到的扫描结果
  const resultsRef = useRef<QRCodeInfo[]>([]);

  // 处理选择结果
  const handleSelectResult = useCallback(
    async (result: QRCodeInfo) => {
      toast.loading('');
      await dealScanResult(result).finally(() => {
        setReviewPicChoiceQrConfig(undefined);
        setScanned(false);
      });
    },
    [dealScanResult, toast],
  );

  // 修改handleCollectedResults以处理多个结果
  const { run: handleCollectedResults } = useDebounceFn(
    async () => {
      if (resultsRef.current.length === 0 && scanned) return;
      // 开始处理
      setScanned(true);
      if (Platform.OS === 'android') {
        await playBeepSound();
      }
      successVibrate();
      const picture = await cameraScannerRef.current?.capture();
      const { uri, width: imgWidth = 0, height: imgHeight = 0 } = picture ?? {};
      if (uri) {
        // 处理图片 orientation
        const fixed = await manipulateAsync(
          uri,
          [], // 不做其他操作，只是让 orientation 归一化
          { compress: 1, format: SaveFormat.JPEG },
        );
        const fixedUri = fixed.uri;
        const scanResult = await BarcodeScanner.scanFromURLAsync(fixedUri).catch((err) => {});
        const realResult: QRCodeInfo[] = [];
        scanResult?.forEach((item) => {
          const { cornerPoints } = item;
          cornerPoints.forEach((point) => {
            const { x, y } = point;
            point.x = (x * width) / imgWidth;
            point.y = (y * height) / imgHeight;
          });
          const [rightTop, rightBottom, leftBottom, leftTop] = cornerPoints;
          if (rightTop && rightBottom && leftBottom && leftTop) {
            // 将新的扫描结果添加到收集数组中
            realResult.push({
              data: item.data,
              type: item.type,
              cornerPoint: {
                rightTop,
                rightBottom,
                leftBottom,
                leftTop,
              },
            });
          }
        });
        if (realResult.length === 0 && resultsRef.current[0]) {
          toast.loading('');
          await dealScanResult(resultsRef.current[0]);
          // 清空收集数组
          resultsRef.current = [];
          setScanned(false);
          return;
        }
        if (realResult.length > 1) {
          setReviewPicChoiceQrConfig({ imgUrl: uri, qrCodes: realResult });
        } else if (realResult.length === 1 && realResult[0]) {
          toast.loading('');
          await dealScanResult(realResult[0]);
          setScanned(false);
        }
      }
    },
    { wait: 200 },
  );

  /**
   * 扫描结果
   */
  const onScanResult = useCallback(
    async (result: BarCodeScanningResult) => {
      const isDuplicate = resultsRef.current.some(
        (existingResult) => existingResult.data === result.data,
      );
      if (!isDuplicate) {
        const { cornerPoints } = result;
        const [rightTop, rightBottom, leftBottom, leftTop] = cornerPoints;
        if (rightTop && rightBottom && leftBottom && leftTop) {
          // 将新的扫描结果添加到收集数组中
          resultsRef.current.push({
            data: result.data,
            type: result.type,
            cornerPoint: {
              rightTop,
              rightBottom,
              leftBottom,
              leftTop,
            },
          });
          // 触发防抖处理函数
          handleCollectedResults();
        }
      }
    },
    [handleCollectedResults],
  );

  const onPressBack = useCallback(() => {
    router.back();
  }, []);

  useDidHide(() => {
    cameraScannerRef.current?.pausePreview();
    setCameraEnabled(false);
  });

  useDidShow(() => {
    cameraScannerRef.current?.resumePreview();
    setCameraEnabled(true);
  });

  /**
   * 打开相册
   */
  const onPressAlbum = useCallback(async () => {
    const isGranted = await checkAndRequestPermission({
      permission: PermissionEnum.Album,
      scene: PermissionPurposeScene.ChoicePicture,
    });
    if (isGranted) {
      const pickResult = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        aspect: [4, 3],
        quality: 1,
      });
      const [asset] = pickResult.assets ?? [];
      if (asset) {
        const scanResult = await BarcodeScanner.scanFromURLAsync(asset.uri).catch((err) => {});
        const [firstResult] = scanResult ?? [];
        if (firstResult) {
          toast.loading('');
          await dealScanResult({
            data: firstResult.data,
            type: firstResult.type,
          });
        } else {
          showToast({
            title: '未识别到二维码',
          });
        }
      }
    }
  }, [checkAndRequestPermission, dealScanResult, toast]);

  /**
   * 打开闪光灯
   */
  const onPressLight = useCallback(() => {
    setFlashMode((prev) => {
      if (prev === FlashMode.off) {
        return FlashMode.torch;
      }
      return FlashMode.off;
    });
  }, []);

  // 扫描线图片高度
  const scanningImgHeight = useMemo(() => {
    return (boxSize * 60) / 327;
  }, [boxSize]);

  // 设置扫描线动画
  useEffect(() => {
    scanLinePosition.value = withRepeat(
      withTiming(1, {
        duration: 3000,
        easing: Easing.linear,
      }),
      -1, // 无限循环
      false, // 不反向
    );
  }, [scanLinePosition]);

  // 扫描线动画样式
  const scanLineStyle = useAnimatedStyle(() => {
    return {
      top: centerY - boxSize / 2 + scanLinePosition.value * (boxSize - scanningImgHeight),
    };
  });

  const onPressCancelReview = useCallback(() => {
    setReviewPicChoiceQrConfig(undefined);
    setScanned(false);
  }, []);

  return {
    scanned,
    onScanResult,
    onPressBack,
    onPressAlbum,
    onPressLight,
    flashMode,
    cameraScannerRef,
    centerX,
    centerY,
    boxSize,
    width,
    height,
    cameraEnabled,
    scanLineStyle,
    scanningImgHeight,
    handleSelectResult,
    reviewPicChoiceQrConfig,
    onPressCancelReview,
  };
};
