import { useCallback, useEffect, useRef } from 'react';
import { iflytekVoiceWakeuper, iflytekSpeechRecognizer } from '@yunti-private/rn-iflytek';
import { envVars } from '@jgl/utils';
import { isPlatform } from '@yunti-private/platform-check';

export const useInitIflytek = () => {
  const initIflytekRef = useRef<boolean>(false);

  const initIflytek = useCallback(async () => {
    if (initIflytekRef.current) {
      return;
    }
    initIflytekRef.current = true;
    await iflytekVoiceWakeuper.initSdk(envVars.iflyTekAppId());
    await iflytekSpeechRecognizer.initSdk(envVars.iflyTekAppId());
  }, []);

  useEffect(() => {
    // TODO: leejunhui - 暂时只在 iOS 上初始化科大讯飞 SDK (2025_06_04)
    if (isPlatform({ os: 'ios' }).isMatch) {
      initIflytek();
    }
  }, [initIflytek]);
};
