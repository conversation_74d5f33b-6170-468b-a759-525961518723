import { BooklnLoginHooks, withLoginLogicVersion } from '@bookln/bookln-biz';
import {
  agreementStateAtom,
  isLoadingUserInfoFromStorageAtom,
  useAppSelector,
  useIsUuidUser,
} from '@jgl/biz-func';
import { useAtom, useAtomValue } from 'jotai';
import { useEffect } from 'react';
import { replaceToHome, replaceToLoginPage } from '../utils/routerHelper';
import { useFetchConfig } from './useFetchConfig';
import { envVars } from '@jgl/utils';
import { router } from 'expo-router';

/**
 * 根据用户信息变化自动跳转到登录页、tab页
 */
export const useChangeRoot = () => {
  const [isLoadingUserInfoFromStorage] = useAtom(isLoadingUserInfoFromStorageAtom);
  const { userId, phone } = useAppSelector((state) => state.userInfo);
  const isSupportSkipBind = BooklnLoginHooks.useSupportSkipBind();
  const agreementState = useAtomValue(agreementStateAtom);

  const isUuidUser = useIsUuidUser();

  const { fetchConfig: updateCommonConfig } = useFetchConfig();

  // isLoadingUserInfoFromStorage
  // - undefined 读取本地用户信息未开始，应停留在index看菊花
  // - true 读取本地用户信息进行中，应停留在index看菊花
  const isUserLoaded = isLoadingUserInfoFromStorage === false;

  useEffect(() => {
    withLoginLogicVersion(
      () => {
        if (isUserLoaded) {
          if (userId === undefined || agreementState === 'undetermined') {
            // 第一次安装APP没有用户信息或者取消了同意协议进入登录页面
            replaceToLoginPage();
          } else {
            // 已登录真实用户
            // 更新配置
            updateCommonConfig();
            replaceToHome();
          }
        }
      },
      () => {
        switch (agreementState) {
          case 'loading': {
            // 还在从AsyncStorage读取是否已同意协议，什么都不做，停留在index看菊花
            break;
          }

          case 'undetermined': {
            // 没有选择同意，也没有选择不同意，未决定的状态
            // 也就是第一次打开app的时候
            // 进入同意协议页面
            router.replace('/agreement');
            break;
          }

          case 'agreed': {
            /* 
          - 第一次进入，agreed之后已经是游客登录成功，可以进入首页
          - 以后每一次进入，agreed说明也是登录成功过，直接进入
        */
            if (isUserLoaded) {
              // 更新配置
              updateCommonConfig();
              replaceToHome();
            }
            break;
          }

          case 'disagreed': {
            // 不同意协议，直接进入首页，但限制功能
            // 可能奇怪的是，同意协议之后去登录页，不同意居然直接进入首页了，但也合理，因为进入了登录页就需要加载微信SDK了
            if (isUserLoaded) {
              // 进入首页，在首页去限制功能
              replaceToHome();
            }
            break;
          }
        }
      },
    )();
  }, [agreementState, isUserLoaded, updateCommonConfig, userId]);
};
