import { Platform } from 'react-native';

export const storageKeys = {
  /**
   * 学习内容
   */
  learningContent: 'LEARNING_CONTENT',

  /**
   * Android升级提示
   */
  androidUpgradeTip: 'ANDROID_UPGRADE_TIP',
  /* 需要更新首页 */
  needUpdateVipInIndex: 'NEED_UPDATE_VIP_IN_INDEX',

  /**
   * 是否同意授权用户隐私协议
   */
  agreementPrivate: 'AGREEMENT_PRIVATE',

  // ---V3版本
  /* 教材 */
  storageTextbook: 'STORAGE_TEXTBOOK',

  /* 通关数据 */
  passData: 'PASS_DATA',

  /**
   * 是否第一次进入首页
   */
  isFirstEnterHome: 'IS_FIRST_ENTER_HOME',

  /**
   * 功能开关
   */
  featureFlags: 'FEATURE_FLAGS',

  /**
   * 版本信息
   */
  reviewVersions: 'REVIEW_VERSIONS',

  /**
   * 当前渠道
   */
  currentChannel: '@firstOpenInstallChannel',

  /**
   * 书架
   */
  bookShelf: 'BOOK_SHELF',

  /**
   * 我的课程
   */
  myLessons: 'MY_LESSONS',

  /**
   * 个性化开关状态
   */
  personalizedSwitch: 'PERSONALIZED_SWITCH',

  /**
   * 消息通知开关状态
   */
  messageNotificationSwitch: 'MESSAGE_NOTIFICATION_SWITCH',
};

export const docNames = {
  userAgreement: '用户服务协议',
  privacy: '隐私政策',
  childrenPrivacy: '儿童隐私保护协议',
};

/** 显示儿童保护协议开关 */
export const showChildrenPrivacySwitch = true;

/**
 * 学科视图相关map
 */
export const subjectViewMap: Record<string, { color: string }> = {
  英语: {
    color: '#62D0E0',
  },
  语文: {
    color: '#F88D3F',
  },
  数学: {
    color: '#65A5FF',
  },
  音乐: {
    color: '#F2B31D',
  },
};

/**
 * 背包 学科主题 map
 */
export const backpackThemeMap: Record<
  string,
  { btnColor: string; linearGradientColors: string[] }
> = {
  英语: {
    btnColor: '#008496',
    linearGradientColors: ['#09c0d900', '#09c0d933', '#09c0d9e5', '#09C0D9'],
  },
  语文: {
    btnColor: '#FA541C',
    linearGradientColors: ['#ec6c0f00', '#ec6c0f3f', '#ec6c0fe5', '#EC6C0F'],
  },
  数学: {
    btnColor: '#0958D9',
    linearGradientColors: ['#0958d900', '#0958d933', '#0958d9e5', '#0958D9'],
  },
  音乐: {
    btnColor: '#FA8C16',
    linearGradientColors: ['#f8a83000', '#f8a8303f', '#f8a830e5', '#F8A830'],
  },
};

/* 知识点场景标签的颜色 */
export const sceneColorArr = ['#69B1FF', '#FFC069', '#73D13D'];
export const DEFAULT_SHARE_DESC = '鲸咕噜，让每一科都变得简单';

/**
 * 玩墨精灵QQ
 */

export const OFFICIAL_QQ = '1602913787';

/** 书链客服QQ */
export const BOOKLN_QQ = '1656218545';

/**
 * ios跳转玩墨精灵QQ详情url
 */
export const URL_QQ_CHATTING_DETAIL_IOS = `mqq://im/chat?chat_type=wpa&uin=${OFFICIAL_QQ}&version=1&src_type=web`;

/**
 * 安卓跳转玩墨精灵QQ详情url
 */
export const URL_QQ_CHATTING_DETAIL_Android = `mqqwpa://im/chat?chat_type=wpa&uin=${OFFICIAL_QQ}&version=1`;

/**
 * 跳转QQ聊天详情
 */
export const URL_QQ_CHATTING_DETAIL =
  Platform.OS === 'ios' ? URL_QQ_CHATTING_DETAIL_IOS : URL_QQ_CHATTING_DETAIL_Android;

/**
 * 音效url
 */
export const SoundUrl = {
  /**
   * 扫码成功音效
   */
  SCAN_SUCCESS_SOUND:
    'https://ajytpan.bookln.cn/btpan/secure/stand/product/organize/4/23/199874134_20250530161521_eaqol.mp3?filename=di.mp3',
};
