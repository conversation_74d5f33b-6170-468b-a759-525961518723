/** @type {import('tailwindcss').Config} */
module.exports = {
  // 相比shared工程里的preset，这里只覆盖和扩展了颜色，其他延用tailwind预设
  presets: [require('./tailwind.preset.js')],
  content: [
    './**/*.ts',
    './**/*.tsx',
    '../../packages/biz-components/**/*.ts',
    '../../packages/biz-components/**/*.tsx',
    '../../packages/bookln-biz/**/*.tsx',
    '../../packages/bookln-biz/**/*.ts',
    '../../packages/components/**/*.ts',
    '../../packages/components/**/*.tsx',
    '../../packages/jgl-ui/**/*.ts',
    '../../packages/jgl-ui/**/*.tsx',
    '../../packages/jgl-ui-v4/**/*.ts',
    '../../packages/jgl-ui-v4/**/*.tsx',
    '../../packages/biz-components-rojer-katex-mini/**/*.tsx',
    '../../packages/permission/**/*.ts',
    '../../packages/permission/**/*.tsx',
    '../../scenes/**/*.ts',
    '../../scenes/**/*.tsx',
  ],
};
