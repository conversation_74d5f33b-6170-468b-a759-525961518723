import type { JglAiQAComponentRef } from '@jgl/ai-qa-v2';
import { JglAiQAComponent } from '@jgl/ai-qa-v2';
import { useIsUuidUser } from '@jgl/biz-func';
import { ContentContainer } from '@jgl/components';
import { JglXStack, JglYStack } from '@jgl/ui-v4';
import { featureToggles } from '@jgl/utils';
import type { ISession } from '@yunti-private/basic-im';
import { forwardRef, useImperativeHandle, useMemo, useRef } from 'react';
import { BooklnContentFeatureEntry } from './BooklnContentFeatureEntry';
import { MineContentScannedBooks } from './MineContentScannedBooks';

export type BooklnContentRef = {
  createNewChatSession: () => void;
  openChatSessionItem: (item: ISession) => void;
};

type Props = {
  offsetTop?: number;
};

/**
 * 书链内容
 */
export const BooklnContent = forwardRef<BooklnContentRef, Props>(
  (props, ref) => {
    const { offsetTop } = props;
    useImperativeHandle(ref, () => ({
      openChatSessionItem: (item: ISession) => {
        jglAiQAComponentRef.current?.openChatSessionItem(item);
      },
      createNewChatSession: () => {
        jglAiQAComponentRef.current?.createNewChatSession();
      },
    }));

    const jglAiQAComponentRef = useRef<JglAiQAComponentRef>(null);

    const isUUID = useIsUuidUser();

    const renderHeader = useMemo(() => {
      return (
        <JglYStack w='full' space={6}>
          {featureToggles.booklnHomeFeatureEntryVisible() ? (
            <BooklnContentFeatureEntry />
          ) : null}
          {!isUUID ? (
            <JglXStack bg='white' borderRadius={16} mx={12}>
              <MineContentScannedBooks />
            </JglXStack>
          ) : null}
          {/* <JglAiQAWelcome /> */}
        </JglYStack>
      );
    }, [isUUID]);

    return (
      <ContentContainer
        containerClassName='flex-1 self-center'
        containerStyle={{
          paddingTop: offsetTop,
        }}
      >
        <JglAiQAComponent
          ref={jglAiQAComponentRef}
          renderHeader={renderHeader}
        />
      </ContentContainer>
    );
  },
);
