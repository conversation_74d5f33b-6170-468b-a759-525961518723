import { routerMap } from '@jgl/biz-func';
import { JGLWebView } from '@jgl/components';
import { container } from '@jgl/container';
import { JglTouchable, JglYStack, JglText, JglButton } from '@jgl/ui-v4';
import { featureToggles, router } from '@jgl/utils';
import { Env } from '@yunti-private/env';
import { useCallback, useState, useEffect, useRef } from 'react';

const LOAD_TIMEOUT = 10000; // 10秒超时
export const myKnowledgeUrl =
  'https://www-daily.yuntim.com/bizmodule/playground/index/forceGraph/forceGraphDemo.htm?url=https://ajyuntitmp.bookln.cn/aj/app/day90/product/xingdengboot/default/332922936/332922936_36b03e78b7ac-4b02-8550-f85a24d7f0ac.json';

/**
 * 我的内容
 */

type Props = {
  width?: number;
  height?: number;
};

export const MyKnowledge = (props: Props) => {
  const { width, height } = props;
  const [hasError, setHasError] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [retryKey, setRetryKey] = useState(0);
  const [loadProgress, setLoadProgress] = useState(0);
  const timeoutRef = useRef<NodeJS.Timeout>();

  const onPressMyKnowledge = useCallback(() => {
    router.push(routerMap.webView, {
      title: '我的知识',
      url: myKnowledgeUrl,
      supportScreenRotation: true,
      supportLogin: false,
    });
  }, []);

  const handleError = useCallback(() => {
    setHasError(true);
    setIsLoading(false);
    setLoadProgress(0);
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }
  }, []);

  const handleLoadStart = useCallback(() => {
    setIsLoading(true);
    setLoadProgress(0);
    // 设置加载超时
    timeoutRef.current = setTimeout(() => {
      if (loadProgress < 0.9) {
        // 如果加载进度小于90%，认为加载超时
        handleError();
      }
    }, LOAD_TIMEOUT);
  }, [loadProgress, handleError]);

  const handleLoadEnd = useCallback(() => {
    setIsLoading(false);
    setLoadProgress(1);
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }
  }, []);

  const handleLoadProgress = useCallback(
    (event: { nativeEvent: { progress: number } }) => {
      setLoadProgress(event.nativeEvent.progress);
    },
    [],
  );

  const handleRetry = useCallback(() => {
    setHasError(false);
    setIsLoading(true);
    setLoadProgress(0);
    setRetryKey((prev) => prev + 1);
  }, []);

  // 清理定时器
  useEffect(() => {
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, []);

  if (container.env().env() === Env.Production) {
    return null;
  }

  if (hasError) {
    return (
      <JglYStack
        w='full'
        h={height || 185}
        alignItems='center'
        justifyContent='center'
        space={16}
        bg={'$background'}
      >
        <JglText color='$color10'>加载失败，请检查网络连接</JglText>
        <JglButton
          loading={isLoading}
          onPress={handleRetry}
          disabled={isLoading}
        >
          {'重试'}
        </JglButton>
      </JglYStack>
    );
  }

  return (
    <JglTouchable w='full' h={height || 185} onPress={onPressMyKnowledge}>
      <JGLWebView
        key={retryKey}
        source={{
          uri: myKnowledgeUrl,
        }}
        scrollEnabled={false}
        webviewDebuggingEnabled={featureToggles.webviewDebuggingEnabled()}
        supportLogin={false}
        onError={handleError}
        onLoadStart={handleLoadStart}
        onLoadEnd={handleLoadEnd}
        onLoadProgress={handleLoadProgress}
      />
    </JglTouchable>
  );
};
