import { withLogin } from '@bookln/bookln-biz';
import { useAgreementCheck } from '@jgl/biz-func';
import { JglTouchable, JglXStack } from '@jgl/ui-v4';
import { envVars, featureToggles } from '@jgl/utils';
import { useAtomValue } from 'jotai';
import { useCallback, useMemo } from 'react';
import type { LayoutChangeEvent } from 'react-native';
import {
  MaterialTabBar,
  type MaterialTabItemProps,
  type TabBarProps,
} from 'react-native-collapsible-tab-view';
import type { TabName } from 'react-native-collapsible-tab-view/lib/typescript/src/types';
import { runOnJS, useSharedValue } from 'react-native-reanimated';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { Image, XStack } from 'tamagui';
import { atomMap } from '../atom';
import { DebugSettingsButton } from './DebugSettingsButton';
import { HomeContentTopTabIndicator } from './HomeContentTopTabIndicator';
import { HomeContentTopTabItem } from './HomeContentTopTabItem';

type Props = {
  miniTopTabContainerHeight: number;
  onLayoutTopTabContainer: (event: LayoutChangeEvent) => void;
  onPressScan: () => void;
  onPressCreateNewChatSession: () => void;
  onPressShowChatSessionListSideMenu: () => void;
} & TabBarProps<TabName>;

/**
 * 每个tab item的间距
 */
const tabItemSpace = 24;

/**
 * 首页顶部标签
 */
export const HomeContentTopTab = (props: Props) => {
  const {
    miniTopTabContainerHeight,
    onLayoutTopTabContainer,
    onPressScan,
    onPressCreateNewChatSession,
    onPressShowChatSessionListSideMenu,
    ...rest
  } = props;

  const { withAgreementCheck } = useAgreementCheck();

  // 存储每个tab item的位置信息
  const tabItemLayouts = useSharedValue<{
    [key: number]: { x: number; width: number };
  }>({});

  const { top } = useSafeAreaInsets();

  const homeTabIndex = useAtomValue(atomMap.homeTabIndexAtom);

  const isHomeSelected = useMemo(() => homeTabIndex === 0, [homeTabIndex]);

  const isMineSelected = useMemo(() => homeTabIndex === 1, [homeTabIndex]);

  const onTabItemLayout = useCallback(
    (index: number, event: LayoutChangeEvent) => {
      const { x, width } = event.nativeEvent.layout;
      runOnJS(tabItemLayouts.modify)((value) => {
        'worklet';
        value[index] = {
          x,
          width,
        };
        return value;
      }, true);
    },
    [tabItemLayouts],
  );

  const renderTabItemComponent = useCallback(
    (tabItemProps: MaterialTabItemProps<TabName>) => {
      return (
        <HomeContentTopTabItem
          {...tabItemProps}
          onTabItemLayout={onTabItemLayout}
          tabItemSpace={tabItemSpace}
        />
      );
    },
    [onTabItemLayout],
  );

  return (
    <XStack
      w={'100%'}
      pt={top}
      minHeight={miniTopTabContainerHeight}
      px={16}
      position='absolute'
      zIndex={1000}
      left={0}
      right={0}
      alignItems='center'
      justifyContent='space-between'
      onLayout={onLayoutTopTabContainer}
    >
      <JglXStack space={16} flex={1}>
        <MaterialTabBar
          {...rest}
          TabItemComponent={renderTabItemComponent}
          indicatorStyle={{ display: 'none' }}
          style={{ zIndex: 100 }}
        />
        <HomeContentTopTabIndicator
          indexDecimal={rest.indexDecimal}
          index={rest.index.value}
          tabItemLayouts={tabItemLayouts}
          tabItemSpace={tabItemSpace}
        />
      </JglXStack>
      <JglXStack space={16}>
        {isMineSelected ? <DebugSettingsButton /> : null}
        {featureToggles.booklnHomeTopRightScanEntryVisible() ? (
          <JglTouchable onPress={onPressScan}>
            <Image
              source={require('../assets/images/ic_scan_dark.png')}
              width={24}
              height={24}
            />
          </JglTouchable>
        ) : null}
        {/* {isHomeSelected ? (
          <JglTouchable
            onPress={withAgreementCheck(onPressCreateNewChatSession)}
          >
            <Image
              source={require('../assets/images/ic_add_session.png')}
              width={24}
              height={24}
            />
          </JglTouchable>
        ) : null} */}
        {isHomeSelected ? (
          <JglTouchable
            onPress={
              envVars.loginLogicVersion() === 'new'
                ? onPressShowChatSessionListSideMenu
                : withAgreementCheck(
                    withLogin(onPressShowChatSessionListSideMenu),
                  )
            }
          >
            <Image
              source={require('../assets/images/ic_menu.png')}
              width={24}
              height={24}
            />
          </JglTouchable>
        ) : null}
      </JglXStack>
    </XStack>
  );
};
