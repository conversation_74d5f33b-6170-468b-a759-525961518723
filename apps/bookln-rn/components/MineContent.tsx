import { ContentContainer } from '@jgl/components';
import Animated, {
  interpolate,
  useAnimatedScrollHandler,
  useAnimatedStyle,
  useSharedValue,
} from 'react-native-reanimated';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { MineContentOtherServices } from './MineContentOtherServices';
import { MineContentScannedBooks } from './MineContentScannedBooks';
import { MineContentStudy } from './MineContentStudy';
import { MineContentUserProfile } from './MineContentUserProfile';
import { MyKnowledge } from './MyKnowledge';
import { MineContentAiBook } from './MineContentAiBook';
import { MineContentLessons } from './MineContentLessons';

type Props = {
  offsetTop?: number;
};

/**
 * 我的内容
 */
export const MineContent = (props: Props) => {
  const { offsetTop = 0 } = props;
  const { bottom } = useSafeAreaInsets();
  const scrollY = useSharedValue(0);
  // const setStatusBarStyle = useSetAtom(atomMap.homeMineTabStatusBarStyleAtom);

  // useAnimatedReaction(
  //   () => scrollY.value,
  //   (y) => {
  //     runOnJS(setStatusBarStyle)(y >= offsetTop / 2 ? 'dark' : 'light');
  //   },
  // );

  const scrollHandler = useAnimatedScrollHandler({
    onScroll: (event) => {
      const { y } = event.contentOffset;
      scrollY.value = y;
    },
  });

  const animatedStyle = useAnimatedStyle(() => {
    const opacity = interpolate(scrollY.value, [0, offsetTop], [0, 1]);
    return {
      backgroundColor: `rgba(255, 255, 255, ${opacity})`,
    };
  });

  return (
    <>
      {/* <HomeBackground /> */}
      <ContentContainer
        containerClassName='self-center relative flex flex-1'
        containerStyle={{
          paddingTop: 0,
          // paddingTop: offsetTop,
        }}
      >
        <Animated.View
          className='absolute top-0 z-[800] w-full'
          style={[{ height: offsetTop }, animatedStyle]}
        />
        <Animated.ScrollView
          onScroll={scrollHandler}
          scrollEventThrottle={16}
          contentContainerStyle={{
            minHeight: '100%',
            paddingTop: offsetTop,
            paddingBottom: bottom + 16,
          }}
          className='flex-1'
          showsVerticalScrollIndicator={false}
        >
          {/* 用户信息 */}
          <MineContentUserProfile />
          <MyKnowledge />
          {/* ai 演示 */}
          <MineContentAiBook />
          {/* 扫码看过的书 */}
          <MineContentScannedBooks />
          {/* 我的课程 */}
          <MineContentLessons />
          {/* 学习 */}
          <MineContentStudy />
          {/* 其他服务 */}
          <MineContentOtherServices />
        </Animated.ScrollView>
      </ContentContainer>
    </>
  );
};
