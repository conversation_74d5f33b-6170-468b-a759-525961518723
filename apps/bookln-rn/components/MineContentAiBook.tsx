import { withLogin } from '@bookln/bookln-biz';
import { useAgreementCheck, useBizRouter } from '@jgl/biz-func';
import { container } from '@jgl/container';
import {
  JglGridView,
  JglText,
  JglTouchable,
  JglXStack,
  JglYStack,
} from '@jgl/ui-v4';
import { envVars } from '@jgl/utils';
import { Env } from '@yunti-private/env';
import { showToast } from '@yunti-private/jgl-ui';
import { useMemo } from 'react';
import { Image } from 'tamagui';
import { routerMap } from '../utils/routerMap';

/**
 * 其他服务
 */
export const MineContentAiBook = () => {
  const bizRouter = useBizRouter();

  const { withAgreementCheck } = useAgreementCheck();

  /**
   * 其他服务功能列表
   */
  const otherServiceList = useMemo(() => {
    const list = [];

    list.push({
      onPress: () => {
        if (envVars.loginLogicVersion() === 'new') {
          withAgreementCheck(() =>
            bizRouter.push(routerMap.AiBookDetail, { backPath: '..' }),
          )();
        } else {
          withLogin(() =>
            bizRouter.push(routerMap.AiBookDetail, { backPath: '..' }),
          )();
        }
      },
      key: 'book',
      icon: require('../assets/images/ic_setting.png'),
      title: 'AI 图书',
    });
    list.push({
      onPress: () => {
        if (envVars.loginLogicVersion() === 'new') {
          withAgreementCheck(() =>
            bizRouter.push(routerMap.MyCollection, { backPath: '..' }),
          )();
        } else {
          withLogin(() =>
            bizRouter.push(routerMap.MyCollection, { backPath: '..' }),
          )();
        }
      },
      key: 'collection',
      icon: require('../assets/images/ic_my_collection.png'),
      title: '我的收藏',
    });
    list.push({
      onPress: () => {
        if (envVars.loginLogicVersion() === 'new') {
          withAgreementCheck(() =>
            bizRouter.push(routerMap.MyWrongExercise, { backPath: '..' }),
          )();
        } else {
          withLogin(() =>
            bizRouter.push(routerMap.MyWrongExercise, { backPath: '..' }),
          )();
        }
      },
      key: 'exercise',
      icon: require('../assets/images/ic_my_practice.png'),
      title: '我的错题',
    });
    list.push({
      onPress: () => {
        showToast({ title: '扩展:英语词本' });
      },
      key: 'english',
      icon: require('../assets/images/ic_my_practice.png'),
      title: '英语词本',
    });
    list.push({
      onPress: () => {
        showToast({ title: '扩展:语文词本' });
      },
      key: 'chinese',
      icon: require('../assets/images/ic_my_practice.png'),
      title: '语文词本',
    });

    return list;
  }, [bizRouter, withAgreementCheck]);

  if (container.env().env() === Env.Production) {
    return null;
  }

  return (
    <JglYStack
      jglClassName='p-[12px] mt-[16px] bg-white'
      marginHorizontal={16}
      borderRadius={8}
      space={12}
    >
      <JglText fontSize={16} fontWeight='bold'>
        学习(演示)
      </JglText>
      <JglXStack>
        <JglGridView
          minColumns={3}
          minItemWidth={114}
          horizontalSpace={56}
          gap={12}
        >
          {otherServiceList.map((item) => (
            <JglTouchable onPress={item.onPress} key={item.title} w={'100%'}>
              <JglYStack space={4} jglClassName='items-center'>
                <Image source={item.icon} width={26} height={26} />
                <JglText fontSize={12} color='#151B37'>
                  {item.title}
                </JglText>
              </JglYStack>
            </JglTouchable>
          ))}
        </JglGridView>
      </JglXStack>
    </JglYStack>
  );
};
