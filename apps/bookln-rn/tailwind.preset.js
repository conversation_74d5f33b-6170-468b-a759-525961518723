/* eslint-disable sonarjs/no-duplicate-string */
/* eslint-disable @typescript-eslint/no-magic-numbers */
/** @type {import('tailwindcss').Config} */

// eslint-disable-next-line @typescript-eslint/no-var-requires
const { TinyColor } = require('@ctrl/tinycolor');
// eslint-disable-next-line @typescript-eslint/no-var-requires
const plugin = require('tailwindcss/plugin');

// 从蚂蚁源码copy过来的
// https://github.com/ant-design/ant-design/blob/master/components/theme/themes/default/colorAlgorithm.ts
const getAlphaColor = (baseColor, alpha) =>
  new TinyColor(baseColor).setAlpha(alpha).toRgbString();

const getSolidColor = (baseColor, brightness) => {
  const instance = new TinyColor(baseColor);
  return instance.darken(brightness).toHexString();
};

/** aka Daybreak Blue */
const colorsBlue = {
  1: '#e6f4ff',
  2: '#bae0ff',
  3: '#91caff',
  4: '#69b1ff',
  5: '#4096ff',
  6: '#1677ff',
  7: '#0958d9',
  8: '#003eb3',
  9: '#002c8c',
  10: '#001d66',
};

const colorsGreen = {
  1: '#f6ffed',
  2: '#d9f7be',
  3: '#b7eb8f',
  4: '#95de64',
  5: '#73d13d',
  6: '#52c41a',
  7: '#389e0d',
  8: '#237804',
  9: '#135200',
  10: '#092b00',
};

const colorsGold = {
  1: '#fffbe6',
  2: '#fff1b8',
  3: '#ffe58f',
  4: '#ffd666',
  5: '#ffc53d',
  6: '#faad14',
  7: '#d48806',
  8: '#ad6800',
  9: '#874d00',
  10: '#613400',
};

// 从蚂蚁源码copy过来的
// https://github.com/ant-design/ant-design/blob/master/components/theme/themes/default/colors.ts
const colorWhite = '#ffffff';
const colorBlack = '#000000';
const colorBgBase = colorWhite;
const colorTextBase = colorBlack;
const colors = {
  text: '#1f1f1f',
  textSecondary: '#595959',
  textTertiary: '#8c8c8c',
  textQuaternary: '#BFBFBF',

  fill: '#D9D9D9',
  fillSecondary: '#F0F0F0',
  fillTertiary: '#F5F5F5',
  fillQuaternary: '#FAFAFA',

  bgLayout: getSolidColor(colorBgBase, 4),
  bgContainer: getSolidColor(colorBgBase, 0),
  bgElevated: getSolidColor(colorBgBase, 0),
  bgSpotlight: getAlphaColor(colorTextBase, 0.85),

  border: getSolidColor(colorBgBase, 15),
  borderSecondary: getSolidColor(colorBgBase, 6),

  blue: colorsBlue,
  primary: {
    bg: colorsBlue[1],
    bgHover: colorsBlue[2],
    border: colorsBlue[3],
    borderHover: colorsBlue[4],
    hover: colorsBlue[5],
    DEFAULT: colorsBlue[6],
    active: colorsBlue[7],
    textHover: colorsBlue[8],
    text: colorsBlue[9],
    textActive: colorsBlue[10],
  },
  info: {
    bg: colorsBlue[1],
    bgHover: colorsBlue[2],
    border: colorsBlue[3],
    borderHover: colorsBlue[4],
    hover: colorsBlue[5],
    DEFAULT: colorsBlue[6],
    active: colorsBlue[7],
    textHover: colorsBlue[8],
    text: colorsBlue[9],
    textActive: colorsBlue[10],
  },
  success: {
    bg: colorsGreen[1],
    bgHover: colorsGreen[2],
    border: colorsGreen[3],
    borderHover: colorsGreen[4],
    hover: colorsGreen[5],
    DEFAULT: colorsGreen[6],
    active: colorsGreen[7],
    textHover: colorsGreen[8],
    text: colorsGreen[9],
    textActive: colorsGreen[10],
  },
  green: colorsGreen,
  warning: {
    bg: colorsGold[1],
    bgHover: colorsGold[2],
    border: colorsGold[3],
    borderHover: colorsGold[4],
    hover: colorsGold[5],
    DEFAULT: colorsGold[6],
    active: colorsGold[7],
    textHover: colorsGold[8],
    text: colorsGold[9],
    textActive: colorsGold[10],
  },
  gold: colorsGold,
  error: {
    // token.colorError:  #ff4d4f
    // 运行时error色值
    // #fff2f0 #fff1f0 #ffccc7 #ffa39e #ff7875 #ff4d4f #d9363e #ff7875 #ff4d4f #d9363e
    //
    // token.red:  #F5222D
    // 运行时red色值
    // #fff1f0 #ffccc7 #ffa39e #ff7875 #ff4d4f #f5222d #cf1322 #a8071a #820014 #5c0011
    //
    // 以下值是Figma付费版组件中定义的值
    // bg: '#fff1f0',
    // 'bg-hover': '#ffccc7',
    // border: '#ffa39e',
    // 'border-hover': '#ff7875',
    // hover: '#ff7875',
    // DEFAULT: '#ff4d4f',
    // active: '#cf1322',
    // 'text-hover': '#a8071a',
    // text: '#820014',
    // 'text-active': '#5c0011',

    // error暂时使用蚂蚁代码里的默认值，保证实际产出一致

    bg: '#fff2f0',
    bgHover: '#fff1f0',
    border: '#ffccc7',
    borderHover: '#ffa39e',
    hover: '#ff7875',
    DEFAULT: '#ff4d4f',
    active: '#d9363e',
    textHover: '#ff7875',
    text: '#ff4d4f',
    textActive: '#d9363e',
  },
};

const controlHeight = 32;

const sizeUnit = 4;
const sizeStep = 4;
const sizes = {
  xxl: sizeUnit * (sizeStep + 8), // 48
  xl: sizeUnit * (sizeStep + 4), // 32
  lg: sizeUnit * (sizeStep + 2), // 24
  md: sizeUnit * (sizeStep + 1), // 20
  ms: sizeUnit * sizeStep, // 16
  base: sizeUnit * sizeStep, // 16
  sm: sizeUnit * (sizeStep - 1), // 12
  xs: sizeUnit * (sizeStep - 2), // 8
  xxs: sizeUnit * (sizeStep - 3), // 4
};

const lineWidth = 1;

module.exports = {
  theme: {
    colors: {
      transparent: 'transparent',

      // https://tailwindcss.com/docs/upgrade-guide#fill-and-stroke-use-color-palette
      // SVG会用到，先照抄文档中的设置
      current: 'currentColor',

      primary: {
        bg: colors.primary.bg,
        'bg-hover': colors.primary.bgHover,
        border: colors.primary.border,
        'border-hover': colors.primary.borderHover,
        hover: colors.primary.hover,
        DEFAULT: colors.primary.DEFAULT,
        active: colors.primary.active,
        'text-hover': colors.primary.textHover,
        text: colors.primary.text,
        'text-active': colors.primary.textActive,
      },
      info: {
        bg: colors.info.bg,
        'bg-hover': colors.info.bgHover,
        border: colors.info.border,
        'border-hover': colors.info.borderHover,
        hover: colors.info.hover,
        DEFAULT: colors.info.DEFAULT,
        active: colors.info.active,
        'text-hover': colors.info.textHover,
        text: colors.info.text,
        'text-active': colors.info.textActive,
      },
      success: {
        bg: colors.success.bg,
        'bg-hover': colors.success.bgHover,
        border: colors.success.border,
        'border-hover': colors.success.borderHover,
        hover: colors.success.hover,
        DEFAULT: colors.success.DEFAULT,
        active: colors.success.active,
        'text-hover': colors.success.textHover,
        text: colors.success.text,
        'text-active': colors.success.textActive,
      },
      warning: {
        bg: colors.warning.bg,
        'bg-hover': colors.warning.bgHover,
        border: colors.warning.border,
        'border-hover': colors.warning.borderHover,
        hover: colors.warning.hover,
        DEFAULT: colors.warning.DEFAULT,
        active: colors.warning.active,
        'text-hover': colors.warning.textHover,
        text: colors.warning.text,
        'text-active': colors.warning.textActive,
      },
      error: {
        // error暂时使用蚂蚁代码里的默认值，保证实际产出一致

        // 以下值是代码中console.log打印出来的实际值
        // 代码如：token.colorErrorBg、token.colorErrorBgHover
        bg: colors.error.bg,
        'bg-hover': colors.error.bgHover,
        border: colors.error.border,
        'border-hover': colors.error.borderHover,
        hover: colors.error.hover,
        DEFAULT: colors.error.DEFAULT,
        active: colors.error.active,
        'text-hover': colors.error.textHover,
        text: colors.error.text,
        'text-active': colors.error.textActive,

        // 以下值是Figma付费版组件中定义的值
        // bg: '#fff1f0',
        // 'bg-hover': '#ffccc7',
        // border: '#ffa39e',
        // 'border-hover': '#ff7875',
        // hover: '#ff7875',
        // DEFAULT: '#ff4d4f',
        // active: '#cf1322',
        // 'text-hover': '#a8071a',
        // text: '#820014',
        // 'text-active': '#5c0011',
      },
      red: {
        // 以下值和代码中的值完全一致

        1: '#fff1f0',
        2: '#ffccc7',
        3: '#ffa39e',
        4: '#ff7875',
        5: '#ff4d4f',
        6: '#f5222d',
        DEFAULT: '#f5222d',
        7: '#cf1322',
        8: '#a8071a',
        9: '#820014',
        10: '#5c0011',
      },
      volcano: {
        1: '#fff2e8',
        2: '#ffd8bf',
        3: '#ffbb96',
        4: '#ff9c6e',
        5: '#ff7a45',
        6: '#fa541c',
        DEFAULT: '#fa541c',
        7: '#d4380d',
        8: '#ad2102',
        9: '#871400',
        10: '#610b00',
      },
      orange: {
        1: '#fff7e6',
        2: '#ffe7ba',
        3: '#ffd591',
        4: '#ffc069',
        5: '#ffa940',
        6: '#fa8c16',
        DEFAULT: '#fa8c16',
        7: '#d46b08',
        8: '#ad4e00',
        9: '#873800',
        10: '#612500',
      },
      gold: {
        1: colors.gold[1],
        2: colors.gold[2],
        3: colors.gold[3],
        4: colors.gold[4],
        5: colors.gold[5],
        6: colors.gold[6],
        DEFAULT: colors.gold[6],
        7: colors.gold[7],
        8: colors.gold[8],
        9: colors.gold[9],
        10: colors.gold[10],
      },
      yellow: {
        1: '#feffe6',
        2: '#ffffb8',
        3: '#fffb8f',
        4: '#fff566',
        5: '#ffec3d',
        6: '#fadb14',
        DEFAULT: '#fadb14',
        7: '#d4b106',
        8: '#ad8b00',
        9: '#876800',
        10: '#614700',
      },
      lime: {
        1: '#fcffe6',
        2: '#f4ffb8',
        3: '#eaff8f',
        4: '#d3f261',
        5: '#bae637',
        6: '#a0d911',
        DEFAULT: '#a0d911',
        7: '#7cb305',
        8: '#5b8c00',
        9: '#3f6600',
        10: '#254000',
      },
      green: {
        1: colors.green[1],
        2: colors.green[2],
        3: colors.green[3],
        4: colors.green[4],
        5: colors.green[5],
        6: colors.green[6],
        DEFAULT: colors.green[6],
        7: colors.green[7],
        8: colors.green[8],
        9: colors.green[9],
        10: colors.green[10],
      },
      cyan: {
        1: '#e6fffb',
        2: '#b5f5ec',
        3: '#87e8de',
        4: '#5cdbd3',
        5: '#36cfc9',
        6: '#13c2c2',
        DEFAULT: '#13c2c2',
        7: '#08979c',
        8: '#006d75',
        9: '#00474f',
        10: '#002329',
      },
      blue: {
        1: colors.blue[1],
        2: colors.blue[2],
        3: colors.blue[3],
        4: colors.blue[4],
        5: colors.blue[5],
        6: colors.blue[6],
        DEFAULT: colors.blue[6],
        7: colors.blue[7],
        8: colors.blue[8],
        9: colors.blue[9],
        10: colors.blue[10],
      },
      'geek-blue': {
        1: '#f0f5ff',
        2: '#d6e4ff',
        3: '#adc6ff',
        4: '#85a5ff',
        5: '#597ef7',
        6: '#2f54eb',
        DEFAULT: '#2f54eb',
        7: '#1d39c4',
        8: '#10239e',
        9: '#061178',
        10: '#030852',
      },
      purple: {
        1: '#f9f0ff',
        2: '#efdbff',
        3: '#d3adf7',
        4: '#b37feb',
        5: '#9254de',
        6: '#722ed1',
        DEFAULT: '#722ed1',
        7: '#531dab',
        8: '#391085',
        9: '#22075e',
        10: '#120338',
      },
      magenta: {
        1: '#fff0f6',
        2: '#ffd6e7',
        3: '#ffadd2',
        4: '#ff85c0',
        5: '#f759ab',
        6: '#eb2f96',
        DEFAULT: '#eb2f96',
        7: '#c41d7f',
        8: '#9e1068',
        9: '#780650',
        10: '#520339',
      },

      white: colorWhite,
      black: colorBlack,

      // neutral colors
      fill: colors.fill,
      'fill-secondary': colors.fillSecondary,
      'fill-tertiary': colors.fillTertiary,
      'fill-quaternary': colors.fillQuaternary,

      // 别名
      'fill-content': colors.fillSecondary,
      'fill-content-hover': colors.fill,
      'fill-after': colors.fillQuaternary,

      split: getAlphaColor(colors.borderSecondary, colors.bgContainer),
    },

    extend: {
      textColor: {
        text: colors.text,
        'text-secondary': colors.textSecondary,
        'text-tertiary': colors.textTertiary,
        'text-quaternary': colors.textQuaternary,

        // alias
        link: colors.info.text,
        'link-hover': colors.info.textHover,
        'link-active': colors.info.textActive,

        placeholder: colors.textQuaternary,
        disabled: colors.textQuaternary,
        heading: colors.text,
        label: colors.textSecondary,
        description: colors.textTertiary,
        'light-solid': colorWhite,
        highlight: colors.error.DEFAULT,

        'color-icon': colors.textTertiary,
        'color-icon-hover': colors.text,
      },
      backgroundColor: {
        mask: new TinyColor(colorBlack).setAlpha(0.45).toRgbString(),

        'control-item': colors.fillTertiary,
        'control-item-active': colors.primary.bg,
        'control-item-active-hover': colors.primary.bgHover,
        'control-item-active-disabled': colors.fill,

        layout: colors.bgLayout,
        container: colors.bgContainer,
        'container-disabled': colors.fillQuaternary,

        elevated: colors.bgElevated,
        spotlight: colors.bgSpotlight,

        'border-bg': colors.bgContainer,

        'bg-text-hover': colors.fillSecondary,
        'bg-text-active': colors.fill,
      },
      borderColor: {
        border: colors.border,
        'border-secondary': colors.borderSecondary,
      },
      outlineColor: {
        'color-control': getAlphaColor(colors.primary.bg, colors.bgContainer),
        'color-control-tmp': colors.fillQuaternary,

        'color-error': getAlphaColor(colors.error.bg, colors.bgContainer),
        'color-warning': getAlphaColor(colors.warning.bg, colors.bgContainer),
      },
    },
  },

  corePlugins: {
    // 暂时禁用，会对一些默认的margin造成影响，比如Delete组件icon、title错位
    // https://tailwindcss.com/docs/preflight#disabling-preflight
    preflight: false,
  },

  plugins: [
    plugin(function ({ addBase, addComponents }) {
      // 预设样式
      addBase({
        '*': {
          margin: 0,
          padding: 0,
          boxSizing: 'border-box',
        },
        '*, :after, :before': {
          borderWidth: 0,
          borderStyle: 'solid',
          borderColor: '#e5e7eb',
        },
      });
      addComponents({
        '.flex-center': {
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
        },
      });
    }),
  ],
};
