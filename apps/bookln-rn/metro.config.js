/* eslint-disable import/no-commonjs */

const path = require('node:path');
const { getSentryExpoConfig } = require('@sentry/react-native/metro');

// 加载共享工具函数
const utilsPath = require.resolve('../../scripts/shared-dev/utils', {
  paths: [__dirname],
});
const { getPackagePath, parseDevPackages, validateSharedPackagePath } = require(utilsPath);

// 获取当前项目和 workspace 目录
const projectRoot = __dirname; // 当前项目目录（如 apps/jgl-rn）
const workspaceRoot = path.resolve(projectRoot, '../..'); // workspace 根目录

// 获取 Sentry 基础配置
const baseConfig = getSentryExpoConfig(projectRoot);

// 添加 workspace 根目录到监听列表
baseConfig.watchFolders = [workspaceRoot];
baseConfig.resolver.nodeModulesPaths = [
  path.resolve(workspaceRoot, 'node_modules'),
  path.resolve(projectRoot, 'node_modules'),
];
baseConfig.resolver.disableHierarchicalLookup = true;

// 添加共享包支持
const devPackages = parseDevPackages();
if (devPackages.length) {
  const packagePaths = devPackages
    .map((pkg) => {
      try {
        const pkgPath = getPackagePath(pkg);
        validateSharedPackagePath(pkgPath);
        return pkgPath;
      } catch (error) {
        console.warn(`⚠️ 无法加载共享包 ${pkg}: ${error.message}`);
        return null;
      }
    })
    .filter(Boolean);

  if (packagePaths.length) {
    // 添加包路径到监听列表
    baseConfig.watchFolders.push(...packagePaths);

    // 添加包的 node_modules 到解析路径
    baseConfig.resolver.nodeModulesPaths.push(
      ...packagePaths.map((p) => path.join(p, 'node_modules')),
    );

    console.log('🔗 正在监听以下共享包:', devPackages);
    console.log('📁 包所在路径:', packagePaths);
  } else {
    console.warn('⚠️ 没有找到任何有效的共享包路径');
  }
}

module.exports = baseConfig;
