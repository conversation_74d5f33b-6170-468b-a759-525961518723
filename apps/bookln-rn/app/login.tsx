import { BooklnLoginComponents } from '@bookln/bookln-biz';
import { agreementStateAtom } from '@jgl/biz-func';
import { router } from '@jgl/utils';
import { Stack } from 'expo-router';
import { StatusBar } from 'expo-status-bar';
import { useAtomValue } from 'jotai';
import { useCallback } from 'react';
import { routerMap } from '../utils/routerMap';

const { LoginContent } = BooklnLoginComponents;

export default function LoginScreen() {
  const onPressLoginByMobile = useCallback(() => {
    router.push(routerMap.LoginByPhone);
  }, []);

  const agreementState = useAtomValue(agreementStateAtom);

  return (
    <>
      <Stack.Screen
        options={
          agreementState === 'disagreed'
            ? {
                title: '',
                headerShadowVisible: false,
              }
            : { headerShown: false, gestureEnabled: false }
        }
      />
      <StatusBar style='dark' />
      <LoginContent onPressLoginByMobile={onPressLoginByMobile} />
    </>
  );
}
