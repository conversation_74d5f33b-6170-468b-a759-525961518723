{"_meta": {"hash": {"sha256": "12f612546483a3435524ed1fbf7af8c5671d3f6d12d282bdebfbfc5789ca0008"}, "pipfile-spec": 6, "requires": {"python_version": "3.8"}, "sources": [{"name": "pypi", "url": "https://pypi.org/simple", "verify_ssl": true}]}, "default": {"aliyun-python-sdk-core": {"hashes": ["sha256:e653ec70bbc36991194b9e56ed0135cc1f39ba387b8dbe635a4bcb7b433744a6"], "version": "==2.13.15"}, "aliyun-python-sdk-core-v3": {"hashes": ["sha256:43228af216096b876b88abaf756bc9b9264663a83b620d22e902b2c269a1ab05"], "version": "==2.13.11"}, "aliyun-python-sdk-kms": {"hashes": ["sha256:82cf3a94041dda614568a63c69158c8d3b04007344825a250595bf4d17b393b2"], "version": "==2.10.1"}, "certifi": {"hashes": ["sha256:017c25db2a153ce562900032d5bc68e9f191e44e9a0f762f373977de9df1fbb3", "sha256:25b64c7da4cd7479594d035c08c2d809eb4aab3a26e5a990ea98cc450c320f1f"], "version": "==2019.11.28"}, "chardet": {"hashes": ["sha256:84ab92ed1c4d4f16916e05906b6b75a6c0fb5db821cc65e70cbd64a3e2a5eaae", "sha256:fc323ffcaeaed0e0a02bf4d117757b98aed530d9ed4531e3e15460124c106691"], "version": "==3.0.4"}, "crcmod": {"hashes": ["sha256:dc7051a0db5f2bd48665a990d3ec1cc305a466a77358ca4492826f41f283601e"], "version": "==1.7"}, "idna": {"hashes": ["sha256:7588d1c14ae4c77d74036e8c22ff447b26d0fde8f007354fd48a7814db15b7cb", "sha256:a068a21ceac8a4d63dbfd964670474107f541babbd2250d61922f029858365fa"], "version": "==2.9"}, "jmespath": {"hashes": ["sha256:695cb76fa78a10663425d5b73ddc5714eb711157e52704d69be03b1a02ba4fec", "sha256:cca55c8d153173e21baa59983015ad0daf603f9cb799904ff057bfb8ff8dc2d9"], "version": "==0.9.5"}, "oss2": {"hashes": ["sha256:34b9fbc22eba56d4c9522b743a2c930a6d20883b8e20cd74b444b38cb2efa509"], "index": "pypi", "version": "==2.9.1"}, "pillow": {"hashes": ["sha256:0a628977ac2e01ca96aaae247ec2bd38e729631ddf2221b4b715446fd45505be", "sha256:4d9ed9a64095e031435af120d3c910148067087541131e82b3e8db302f4c8946", "sha256:54ebae163e8412aff0b9df1e88adab65788f5f5b58e625dc5c7f51eaf14a6837", "sha256:5bfef0b1cdde9f33881c913af14e43db69815c7e8df429ceda4c70a5e529210f", "sha256:5f3546ceb08089cedb9e8ff7e3f6a7042bb5b37c2a95d392fb027c3e53a2da00", "sha256:5f7ae9126d16194f114435ebb79cc536b5682002a4fa57fa7bb2cbcde65f2f4d", "sha256:62a889aeb0a79e50ecf5af272e9e3c164148f4bd9636cc6bcfa182a52c8b0533", "sha256:7406f5a9b2fd966e79e6abdaf700585a4522e98d6559ce37fc52e5c955fade0a", "sha256:8453f914f4e5a3d828281a6628cf517832abfa13ff50679a4848926dac7c0358", "sha256:87269cc6ce1e3dee11f23fa515e4249ae678dbbe2704598a51cee76c52e19cda", "sha256:875358310ed7abd5320f21dd97351d62de4929b0426cdb1eaa904b64ac36b435", "sha256:8ac6ce7ff3892e5deaab7abaec763538ffd011f74dc1801d93d3c5fc541feee2", "sha256:91b710e3353aea6fc758cdb7136d9bbdcb26b53cefe43e2cba953ac3ee1d3313", "sha256:9d2ba4ed13af381233e2d810ff3bab84ef9f18430a9b336ab69eaf3cd24299ff", "sha256:a62ec5e13e227399be73303ff301f2865bf68657d15ea50b038d25fc41097317", "sha256:ab76e5580b0ed647a8d8d2d2daee170e8e9f8aad225ede314f684e297e3643c2", "sha256:bf4003aa538af3f4205c5fac56eacaa67a6dd81e454ffd9e9f055fff9f1bc614", "sha256:bf598d2e37cf8edb1a2f26ed3fb255191f5232badea4003c16301cb94ac5bdd0", "sha256:c18f70dc27cc5d236f10e7834236aff60aadc71346a5bc1f4f83a4b3abee6386", "sha256:c5ed816632204a2fc9486d784d8e0d0ae754347aba99c811458d69fcdfd2a2f9", "sha256:dc058b7833184970d1248135b8b0ab702e6daa833be14035179f2acb78ff5636", "sha256:ff3797f2f16bf9d17d53257612da84dd0758db33935777149b3334c01ff68865"], "index": "pypi", "version": "==7.0.0"}, "pycryptodome": {"hashes": ["sha256:07024fc364869eae8d6ac0d316e089956e6aeffe42dbdcf44fe1320d96becf7f", "sha256:09b6d6bcc01a4eb1a2b4deeff5aa602a108ec5aed8ac75ae554f97d1d7f0a5ad", "sha256:0e10f352ccbbcb5bb2dc4ecaf106564e65702a717d72ab260f9ac4c19753cfc2", "sha256:1f4752186298caf2e9ff5354f2e694d607ca7342aa313a62005235d46e28cf04", "sha256:2fbc472e0b567318fe2052281d5a8c0ae70099b446679815f655e9fbc18c3a65", "sha256:3ec3dc2f80f71fd0c955ce48b81bfaf8914c6f63a41a738f28885a1c4892968a", "sha256:426c188c83c10df71f053e04b4003b1437bae5cb37606440e498b00f160d71d0", "sha256:626c0a1d4d83ec6303f970a17158114f75c3ba1736f7f2983f7b40a265861bd8", "sha256:767ad0fb5d23efc36a4d5c2fc608ac603f3de028909bcf59abc943e0d0bc5a36", "sha256:7ac729d9091ed5478af2b4a4f44f5335a98febbc008af619e4569a59fe503e40", "sha256:83295a3fb5cf50c48631eb5b440cb5e9832d8c14d81d1d45f4497b67a9987de8", "sha256:8be56bde3312e022d9d1d6afa124556460ad5c844c2fc63642f6af723c098d35", "sha256:8f06556a8f7ea7b1e42eff39726bb0dca1c251205debae64e6eebea3cd7b438a", "sha256:9230fcb5d948c3fb40049bace4d33c5d254f8232c2c0bba05d2570aea3ba4520", "sha256:9378c309aec1f8cd8bad361ed0816a440151b97a2a3f6ffdaba1d1a1fb76873a", "sha256:9977086e0f93adb326379897437373871b80501e1d176fec63c7f46fb300c862", "sha256:9a94fca11fdc161460bd8659c15b6adef45c1b20da86402256eaf3addfaab324", "sha256:9c739b7795ccf2ef1fdad8d44e539a39ad300ee6786e804ea7f0c6a786eb5343", "sha256:b1e332587b3b195542e77681389c296e1837ca01240399d88803a075447d3557", "sha256:c109a26a21f21f695d369ff9b87f5d43e0d6c768d8384e10bc74142bed2e092e", "sha256:c818dc1f3eace93ee50c2b6b5c2becf7c418fa5dd1ba6fc0ef7db279ea21d5e4", "sha256:cff31f5a8977534f255f729d5d2467526f2b10563a30bbdade92223e0bf264bd", "sha256:d4f94368ce2d65873a87ad867eb3bf63f4ba81eb97a9ee66d38c2b71ce5a7439", "sha256:d61b012baa8c2b659e9890011358455c0019a4108536b811602d2f638c40802a", "sha256:d6e1bc5c94873bec742afe2dfadce0d20445b18e75c47afc0c115b19e5dd38dd", "sha256:ea83bcd9d6c03248ebd46e71ac313858e0afd5aa2fa81478c0e653242f3eb476", "sha256:ed5761b37615a1f222c5345bbf45272ae2cf8c7dff88a4f53a1e9f977cbb6d95", "sha256:f011cd0062e54658b7086a76f8cf0f4222812acc66e219e196ea2d0a8849d0ed", "sha256:f1add21b6d179179b3c177c33d18a2186a09cc0d3af41ff5ed3f377360b869f2", "sha256:f655addaaaa9974108d4808f4150652589cada96074c87115c52e575bfcd87d5"], "version": "==3.9.7"}, "qrcode": {"hashes": ["sha256:3996ee560fc39532910603704c82980ff6d4d5d629f9c3f25f34174ce8606cf5", "sha256:505253854f607f2abf4d16092c61d4e9d511a3b4392e60bff957a68592b04369"], "index": "pypi", "version": "==6.1"}, "requests": {"hashes": ["sha256:43999036bfa82904b6af1d99e4882b560e5e2c68e5c4b0aa03b655f3d7d73fee", "sha256:b3f43d496c6daba4493e7c431722aeb7dbc6288f52a6e04e7b6023b0247817e6"], "version": "==2.23.0"}, "six": {"hashes": ["sha256:236bdbdce46e6e6a3d61a337c0f8b763ca1e8717c03b369e87a7ec7ce1319c0a", "sha256:8f3cd2e254d8f793e7f3d6d9df77b92252b52637291d0f0da013c76ea2724b6c"], "version": "==1.14.0"}, "urllib3": {"hashes": ["sha256:2f3db8b19923a873b3e5256dc9c2dedfa883e33d87c690d9c7913e1f40673cdc", "sha256:87716c2d2a7121198ebcb7ce7cccf6ce5e9ba539041cfbaeecfb641dc0bf6acc"], "version": "==1.25.8"}}, "develop": {}}