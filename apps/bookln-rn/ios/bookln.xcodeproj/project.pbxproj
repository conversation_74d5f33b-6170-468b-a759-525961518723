// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 54;
	objects = {

/* Begin PBXBuildFile section */
		13B07FBC1A68108700A75B9A /* AppDelegate.mm in Sources */ = {isa = PBXBuildFile; fileRef = 13B07FB01A68108700A75B9A /* AppDelegate.mm */; };
		13B07FBF1A68108700A75B9A /* Images.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = 13B07FB51A68108700A75B9A /* Images.xcassets */; };
		13B07FC11A68108700A75B9A /* main.m in Sources */ = {isa = PBXBuildFile; fileRef = 13B07FB71A68108700A75B9A /* main.m */; };
		2D2355B22B6F426800D9BB88 /* SetupExpoUpdates.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2D2355B12B6F426800D9BB88 /* SetupExpoUpdates.swift */; };
		2D2355B82B6F428E00D9BB88 /* BooklnLaunchCount.m in Sources */ = {isa = PBXBuildFile; fileRef = 2D2355B52B6F428E00D9BB88 /* BooklnLaunchCount.m */; };
		2D2355BA2B6F6B0C00D9BB88 /* expoUpdateConfig.json in Resources */ = {isa = PBXBuildFile; fileRef = 2D2355B92B6F6B0C00D9BB88 /* expoUpdateConfig.json */; };
		2D442CE22BEA0FC700C45FA2 /* BooklnNativeHelper.m in Sources */ = {isa = PBXBuildFile; fileRef = 2D442CE12BEA0FC700C45FA2 /* BooklnNativeHelper.m */; };
		2D442CE92BEA127500C45FA2 /* baseInfo.json in Resources */ = {isa = PBXBuildFile; fileRef = 2D442CE72BEA127500C45FA2 /* baseInfo.json */; };
		2D442CEA2BEA127500C45FA2 /* appext.json in Resources */ = {isa = PBXBuildFile; fileRef = 2D442CE82BEA127500C45FA2 /* appext.json */; };
		2D95EE142B3E7346007D7CF4 /* aiengine.provision in Resources */ = {isa = PBXBuildFile; fileRef = 2D95EE132B3E7346007D7CF4 /* aiengine.provision */; };
		3E461D99554A48A4959DE609 /* SplashScreen.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = AA286B85B6C04FC6940260E9 /* SplashScreen.storyboard */; };
		B18059E884C0ABDD17F3DC3D /* ExpoModulesProvider.swift in Sources */ = {isa = PBXBuildFile; fileRef = FAC715A2D49A985799AEE119 /* ExpoModulesProvider.swift */; };
		BB2F792D24A3F905000567C9 /* Expo.plist in Resources */ = {isa = PBXBuildFile; fileRef = BB2F792C24A3F905000567C9 /* Expo.plist */; };
		C39731AB80A424309735C2D7 /* libPods-bookln.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 590A7A3F0FC3BC1AFBA16947 /* libPods-bookln.a */; };
		D770DC8C90B24BC885A19239 /* noop-file.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1963959A1DB74645B4DD5EF3 /* noop-file.swift */; };
/* End PBXBuildFile section */

/* Begin PBXFileReference section */
		13B07F961A680F5B00A75B9A /* bookln.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = bookln.app; sourceTree = BUILT_PRODUCTS_DIR; };
		13B07FAF1A68108700A75B9A /* AppDelegate.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = AppDelegate.h; path = bookln/AppDelegate.h; sourceTree = "<group>"; };
		13B07FB01A68108700A75B9A /* AppDelegate.mm */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.objcpp; name = AppDelegate.mm; path = bookln/AppDelegate.mm; sourceTree = "<group>"; };
		13B07FB51A68108700A75B9A /* Images.xcassets */ = {isa = PBXFileReference; lastKnownFileType = folder.assetcatalog; name = Images.xcassets; path = bookln/Images.xcassets; sourceTree = "<group>"; };
		13B07FB61A68108700A75B9A /* Info.plist */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.plist.xml; name = Info.plist; path = bookln/Info.plist; sourceTree = "<group>"; };
		13B07FB71A68108700A75B9A /* main.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; name = main.m; path = bookln/main.m; sourceTree = "<group>"; };
		1963959A1DB74645B4DD5EF3 /* noop-file.swift */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 4; includeInIndex = 0; lastKnownFileType = sourcecode.swift; name = "noop-file.swift"; path = "bookln/noop-file.swift"; sourceTree = "<group>"; };
		2D0EAEEC2DEEDCFE00232D52 /* CoreLocation.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = CoreLocation.framework; path = System/Library/Frameworks/CoreLocation.framework; sourceTree = SDKROOT; };
		2D0EAEEE2DEEDD0400232D52 /* CoreTelephony.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = CoreTelephony.framework; path = System/Library/Frameworks/CoreTelephony.framework; sourceTree = SDKROOT; };
		2D0EAEF02DEEDD0800232D52 /* AVFoundation.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = AVFoundation.framework; path = System/Library/Frameworks/AVFoundation.framework; sourceTree = SDKROOT; };
		2D0EAEF22DEEDD0D00232D52 /* AddressBook.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = AddressBook.framework; path = System/Library/Frameworks/AddressBook.framework; sourceTree = SDKROOT; };
		2D0EAEF42DEEDD1100232D52 /* Contacts.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = Contacts.framework; path = System/Library/Frameworks/Contacts.framework; sourceTree = SDKROOT; };
		2D0EAEF62DEEDD1800232D52 /* AudioToolbox.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = AudioToolbox.framework; path = System/Library/Frameworks/AudioToolbox.framework; sourceTree = SDKROOT; };
		2D0EAEF82DEEDD1F00232D52 /* SystemConfiguration.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = SystemConfiguration.framework; path = System/Library/Frameworks/SystemConfiguration.framework; sourceTree = SDKROOT; };
		2D0EAEFA2DEEDD2500232D52 /* QuartzCore.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = QuartzCore.framework; path = System/Library/Frameworks/QuartzCore.framework; sourceTree = SDKROOT; };
		2D0EAEFC2DEEDD2A00232D52 /* UIKit.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = UIKit.framework; path = System/Library/Frameworks/UIKit.framework; sourceTree = SDKROOT; };
		2D0EAEFE2DEEDD3000232D52 /* Foundation.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = Foundation.framework; path = System/Library/Frameworks/Foundation.framework; sourceTree = SDKROOT; };
		2D0EAF002DEEDD3500232D52 /* CoreGraphics.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = CoreGraphics.framework; path = System/Library/Frameworks/CoreGraphics.framework; sourceTree = SDKROOT; };
		2D0EAF022DEEDDBA00232D52 /* libc++.tbd */ = {isa = PBXFileReference; lastKnownFileType = "sourcecode.text-based-dylib-definition"; name = "libc++.tbd"; path = "usr/lib/libc++.tbd"; sourceTree = SDKROOT; };
		2D0EAF032DEEDDBF00232D52 /* libz.tbd */ = {isa = PBXFileReference; lastKnownFileType = "sourcecode.text-based-dylib-definition"; name = libz.tbd; path = usr/lib/libz.tbd; sourceTree = SDKROOT; };
		2D2355B12B6F426800D9BB88 /* SetupExpoUpdates.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SetupExpoUpdates.swift; sourceTree = "<group>"; };
		2D2355B32B6F428E00D9BB88 /* BooklnLaunchCount.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = BooklnLaunchCount.h; path = bookln/BooklnLaunchCount.h; sourceTree = "<group>"; };
		2D2355B52B6F428E00D9BB88 /* BooklnLaunchCount.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; name = BooklnLaunchCount.m; path = bookln/BooklnLaunchCount.m; sourceTree = "<group>"; };
		2D2355B92B6F6B0C00D9BB88 /* expoUpdateConfig.json */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.json; name = expoUpdateConfig.json; path = bookln/expoUpdateConfig.json; sourceTree = "<group>"; };
		2D30FDD92B6776B40066912F /* StoreKit.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = StoreKit.framework; path = System/Library/Frameworks/StoreKit.framework; sourceTree = SDKROOT; };
		2D442CE02BEA0FC700C45FA2 /* BooklnNativeHelper.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; name = BooklnNativeHelper.h; path = bookln/BooklnNativeHelper.h; sourceTree = "<group>"; };
		2D442CE12BEA0FC700C45FA2 /* BooklnNativeHelper.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; name = BooklnNativeHelper.m; path = bookln/BooklnNativeHelper.m; sourceTree = "<group>"; };
		2D442CE72BEA127500C45FA2 /* baseInfo.json */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.json; name = baseInfo.json; path = bookln/baseInfo.json; sourceTree = "<group>"; };
		2D442CE82BEA127500C45FA2 /* appext.json */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.json; name = appext.json; path = bookln/appext.json; sourceTree = "<group>"; };
		2D95EE132B3E7346007D7CF4 /* aiengine.provision */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text; path = aiengine.provision; sourceTree = "<group>"; };
		38CAC5181CBA4F859669054C /* bookln-Bridging-Header.h */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 4; includeInIndex = 0; lastKnownFileType = sourcecode.c.h; name = "bookln-Bridging-Header.h"; path = "bookln/bookln-Bridging-Header.h"; sourceTree = "<group>"; };
		590A7A3F0FC3BC1AFBA16947 /* libPods-bookln.a */ = {isa = PBXFileReference; explicitFileType = archive.ar; includeInIndex = 0; path = "libPods-bookln.a"; sourceTree = BUILT_PRODUCTS_DIR; };
		9ACE5613C882902A545E8300 /* Pods-bookln.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-bookln.release.xcconfig"; path = "Target Support Files/Pods-bookln/Pods-bookln.release.xcconfig"; sourceTree = "<group>"; };
		A2D6F2F1C005F3730125E7C6 /* Pods-bookln.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-bookln.debug.xcconfig"; path = "Target Support Files/Pods-bookln/Pods-bookln.debug.xcconfig"; sourceTree = "<group>"; };
		AA286B85B6C04FC6940260E9 /* SplashScreen.storyboard */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = file.storyboard; name = SplashScreen.storyboard; path = bookln/SplashScreen.storyboard; sourceTree = "<group>"; };
		BB2F792C24A3F905000567C9 /* Expo.plist */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.plist.xml; path = Expo.plist; sourceTree = "<group>"; };
		ED297162215061F000B7C4FE /* JavaScriptCore.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = JavaScriptCore.framework; path = System/Library/Frameworks/JavaScriptCore.framework; sourceTree = SDKROOT; };
		FAC715A2D49A985799AEE119 /* ExpoModulesProvider.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = ExpoModulesProvider.swift; path = "Pods/Target Support Files/Pods-bookln/ExpoModulesProvider.swift"; sourceTree = "<group>"; };
/* End PBXFileReference section */

/* Begin PBXFrameworksBuildPhase section */
		13B07F8C1A680F5B00A75B9A /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = **********;
			files = (
				C39731AB80A424309735C2D7 /* libPods-bookln.a in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		13B07FAE1A68108700A75B9A /* bookln */ = {
			isa = PBXGroup;
			children = (
				2D442CE82BEA127500C45FA2 /* appext.json */,
				2D442CE72BEA127500C45FA2 /* baseInfo.json */,
				2D2355B92B6F6B0C00D9BB88 /* expoUpdateConfig.json */,
				2D2355B32B6F428E00D9BB88 /* BooklnLaunchCount.h */,
				2D2355B52B6F428E00D9BB88 /* BooklnLaunchCount.m */,
				BB2F792B24A3F905000567C9 /* Supporting */,
				13B07FAF1A68108700A75B9A /* AppDelegate.h */,
				13B07FB01A68108700A75B9A /* AppDelegate.mm */,
				13B07FB51A68108700A75B9A /* Images.xcassets */,
				13B07FB61A68108700A75B9A /* Info.plist */,
				13B07FB71A68108700A75B9A /* main.m */,
				AA286B85B6C04FC6940260E9 /* SplashScreen.storyboard */,
				1963959A1DB74645B4DD5EF3 /* noop-file.swift */,
				38CAC5181CBA4F859669054C /* bookln-Bridging-Header.h */,
				2D2355B12B6F426800D9BB88 /* SetupExpoUpdates.swift */,
				2D442CE02BEA0FC700C45FA2 /* BooklnNativeHelper.h */,
				2D442CE12BEA0FC700C45FA2 /* BooklnNativeHelper.m */,
			);
			name = bookln;
			sourceTree = "<group>";
		};
		2D16E6871FA4F8E400B85C8A /* Frameworks */ = {
			isa = PBXGroup;
			children = (
				2D0EAF032DEEDDBF00232D52 /* libz.tbd */,
				2D0EAF022DEEDDBA00232D52 /* libc++.tbd */,
				2D0EAF002DEEDD3500232D52 /* CoreGraphics.framework */,
				2D0EAEFE2DEEDD3000232D52 /* Foundation.framework */,
				2D0EAEFC2DEEDD2A00232D52 /* UIKit.framework */,
				2D0EAEFA2DEEDD2500232D52 /* QuartzCore.framework */,
				2D0EAEF82DEEDD1F00232D52 /* SystemConfiguration.framework */,
				2D0EAEF62DEEDD1800232D52 /* AudioToolbox.framework */,
				2D0EAEF42DEEDD1100232D52 /* Contacts.framework */,
				2D0EAEF22DEEDD0D00232D52 /* AddressBook.framework */,
				2D0EAEF02DEEDD0800232D52 /* AVFoundation.framework */,
				2D0EAEEE2DEEDD0400232D52 /* CoreTelephony.framework */,
				2D0EAEEC2DEEDCFE00232D52 /* CoreLocation.framework */,
				2D30FDD92B6776B40066912F /* StoreKit.framework */,
				ED297162215061F000B7C4FE /* JavaScriptCore.framework */,
				590A7A3F0FC3BC1AFBA16947 /* libPods-bookln.a */,
			);
			name = Frameworks;
			sourceTree = "<group>";
		};
		2D95EE122B3E7346007D7CF4 /* LicenseFile */ = {
			isa = PBXGroup;
			children = (
				2D95EE132B3E7346007D7CF4 /* aiengine.provision */,
			);
			path = LicenseFile;
			sourceTree = "<group>";
		};
		832341AE1AAA6A7D00B99B32 /* Libraries */ = {
			isa = PBXGroup;
			children = (
			);
			name = Libraries;
			sourceTree = "<group>";
		};
		83CBB9F61A601CBA00E9B192 = {
			isa = PBXGroup;
			children = (
				2D95EE122B3E7346007D7CF4 /* LicenseFile */,
				13B07FAE1A68108700A75B9A /* bookln */,
				832341AE1AAA6A7D00B99B32 /* Libraries */,
				83CBBA001A601CBA00E9B192 /* Products */,
				2D16E6871FA4F8E400B85C8A /* Frameworks */,
				D65327D7A22EEC0BE12398D9 /* Pods */,
				D7E4C46ADA2E9064B798F356 /* ExpoModulesProviders */,
			);
			indentWidth = 2;
			sourceTree = "<group>";
			tabWidth = 2;
			usesTabs = 0;
		};
		83CBBA001A601CBA00E9B192 /* Products */ = {
			isa = PBXGroup;
			children = (
				13B07F961A680F5B00A75B9A /* bookln.app */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		92DBD88DE9BF7D494EA9DA96 /* bookln */ = {
			isa = PBXGroup;
			children = (
				FAC715A2D49A985799AEE119 /* ExpoModulesProvider.swift */,
			);
			name = bookln;
			sourceTree = "<group>";
		};
		BB2F792B24A3F905000567C9 /* Supporting */ = {
			isa = PBXGroup;
			children = (
				BB2F792C24A3F905000567C9 /* Expo.plist */,
			);
			name = Supporting;
			path = bookln/Supporting;
			sourceTree = "<group>";
		};
		D65327D7A22EEC0BE12398D9 /* Pods */ = {
			isa = PBXGroup;
			children = (
				A2D6F2F1C005F3730125E7C6 /* Pods-bookln.debug.xcconfig */,
				9ACE5613C882902A545E8300 /* Pods-bookln.release.xcconfig */,
			);
			path = Pods;
			sourceTree = "<group>";
		};
		D7E4C46ADA2E9064B798F356 /* ExpoModulesProviders */ = {
			isa = PBXGroup;
			children = (
				92DBD88DE9BF7D494EA9DA96 /* bookln */,
			);
			name = ExpoModulesProviders;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		13B07F861A680F5B00A75B9A /* bookln */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 13B07F931A680F5B00A75B9A /* Build configuration list for PBXNativeTarget "bookln" */;
			buildPhases = (
				7444DFFF6767707E31B4B68B /* [CP] Check Pods Manifest.lock */,
				9B2D1113F56C486AFC287AFB /* [Expo] Configure project */,
				13B07F871A680F5B00A75B9A /* Sources */,
				13B07F8C1A680F5B00A75B9A /* Frameworks */,
				13B07F8E1A680F5B00A75B9A /* Resources */,
				00DD1BFF1BD5951E006B06BC /* Bundle React Native code and images */,
				173A9B23436347388995645B /* Upload Debug Symbols to Sentry */,
				D7544E6ED07A9D823B79533E /* [CP] Embed Pods Frameworks */,
				58DBEAEA15EE2D451F52F842 /* [CP] Copy Pods Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = bookln;
			productName = bookln;
			productReference = 13B07F961A680F5B00A75B9A /* bookln.app */;
			productType = "com.apple.product-type.application";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		83CBB9F71A601CBA00E9B192 /* Project object */ = {
			isa = PBXProject;
			attributes = {
				LastUpgradeCheck = 1130;
				TargetAttributes = {
					13B07F861A680F5B00A75B9A = {
						LastSwiftMigration = 1250;
					};
				};
			};
			buildConfigurationList = 83CBB9FA1A601CBA00E9B192 /* Build configuration list for PBXProject "bookln" */;
			compatibilityVersion = "Xcode 3.2";
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = 83CBB9F61A601CBA00E9B192;
			productRefGroup = 83CBBA001A601CBA00E9B192 /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				13B07F861A680F5B00A75B9A /* bookln */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		13B07F8E1A680F5B00A75B9A /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = **********;
			files = (
				BB2F792D24A3F905000567C9 /* Expo.plist in Resources */,
				2D2355BA2B6F6B0C00D9BB88 /* expoUpdateConfig.json in Resources */,
				2D442CE92BEA127500C45FA2 /* baseInfo.json in Resources */,
				2D95EE142B3E7346007D7CF4 /* aiengine.provision in Resources */,
				13B07FBF1A68108700A75B9A /* Images.xcassets in Resources */,
				2D442CEA2BEA127500C45FA2 /* appext.json in Resources */,
				3E461D99554A48A4959DE609 /* SplashScreen.storyboard in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXShellScriptBuildPhase section */
		00DD1BFF1BD5951E006B06BC /* Bundle React Native code and images */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputPaths = (
			);
			name = "Bundle React Native code and images";
			outputPaths = (
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "if [[ -f \"$PODS_ROOT/../.xcode.env\" ]]; then\n  source \"$PODS_ROOT/../.xcode.env\"\nfi\nif [[ -f \"$PODS_ROOT/../.xcode.env.local\" ]]; then\n  source \"$PODS_ROOT/../.xcode.env.local\"\nfi\n\n# The project root by default is one level up from the ios directory\nexport PROJECT_ROOT=\"$PROJECT_DIR\"/..\n\nif [[ \"$CONFIGURATION\" = *Debug* ]]; then\n  export SKIP_BUNDLING=1\nfi\nif [[ -z \"$ENTRY_FILE\" ]]; then\n  # Set the entry JS file using the bundler's entry resolution.\n  export ENTRY_FILE=\"$(\"$NODE_BINARY\" -e \"require('expo/scripts/resolveAppEntry')\" \"$PROJECT_ROOT\" ios relative | tail -n 1)\"\nfi\n\nif [[ -z \"$CLI_PATH\" ]]; then\n  # Use Expo CLI\n  export CLI_PATH=\"$(\"$NODE_BINARY\" --print \"require.resolve('@expo/cli')\")\"\nfi\nif [[ -z \"$BUNDLE_COMMAND\" ]]; then\n  # Default Expo CLI command for bundling\n  export BUNDLE_COMMAND=\"export:embed\"\nfi\n\n/bin/sh `\"$NODE_BINARY\" --print \"require('path').dirname(require.resolve('@sentry/react-native/package.json')) + '/scripts/sentry-xcode.sh'\"` `\"$NODE_BINARY\" --print \"require('path').dirname(require.resolve('react-native/package.json')) + '/scripts/react-native-xcode.sh'\"`\n\n";
		};
		173A9B23436347388995645B /* Upload Debug Symbols to Sentry */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputPaths = (
			);
			name = "Upload Debug Symbols to Sentry";
			outputPaths = (
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "if [[ \"$CONFIGURATION\" = *Release* ]]; then\n  /bin/sh ../../../node_modules/@sentry/react-native/scripts/sentry-xcode-debug-files.sh\nfi\n\n";
		};
		58DBEAEA15EE2D451F52F842 /* [CP] Copy Pods Resources */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-bookln/Pods-bookln-resources.sh",
				"${PODS_CONFIGURATION_BUILD_DIR}/AliyunOSSiOS/AliyunOSSiOS_Privacy.bundle",
				"${PODS_CONFIGURATION_BUILD_DIR}/EXConstants/EXConstants.bundle",
				"${PODS_CONFIGURATION_BUILD_DIR}/EXUpdates/EXUpdates.bundle",
				"${PODS_CONFIGURATION_BUILD_DIR}/ReachabilitySwift/ReachabilitySwift.bundle",
				"${PODS_CONFIGURATION_BUILD_DIR}/React-Core/AccessibilityResources.bundle",
				"${PODS_CONFIGURATION_BUILD_DIR}/Sentry/Sentry.bundle",
				"${PODS_CONFIGURATION_BUILD_DIR}/ToastHybrid/ToastHybrid.bundle",
				"${PODS_ROOT}/UMCCommonLog/UMCommonLog/UMCommonLog.bundle",
				"${PODS_CONFIGURATION_BUILD_DIR}/expo-dev-launcher/EXDevLauncher.bundle",
				"${PODS_CONFIGURATION_BUILD_DIR}/expo-dev-menu/EXDevMenu.bundle",
				"${PODS_CONFIGURATION_BUILD_DIR}/rn-iflytek/RnIflytekResource.bundle",
			);
			name = "[CP] Copy Pods Resources";
			outputPaths = (
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/AliyunOSSiOS_Privacy.bundle",
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/EXConstants.bundle",
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/EXUpdates.bundle",
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/ReachabilitySwift.bundle",
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/AccessibilityResources.bundle",
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/Sentry.bundle",
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/ToastHybrid.bundle",
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/UMCommonLog.bundle",
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/EXDevLauncher.bundle",
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/EXDevMenu.bundle",
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/RnIflytekResource.bundle",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-bookln/Pods-bookln-resources.sh\"\n";
			showEnvVarsInLog = 0;
		};
		7444DFFF6767707E31B4B68B /* [CP] Check Pods Manifest.lock */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
				"${PODS_PODFILE_DIR_PATH}/Podfile.lock",
				"${PODS_ROOT}/Manifest.lock",
			);
			name = "[CP] Check Pods Manifest.lock";
			outputFileListPaths = (
			);
			outputPaths = (
				"$(DERIVED_FILE_DIR)/Pods-bookln-checkManifestLockResult.txt",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "diff \"${PODS_PODFILE_DIR_PATH}/Podfile.lock\" \"${PODS_ROOT}/Manifest.lock\" > /dev/null\nif [ $? != 0 ] ; then\n    # print error to STDERR\n    echo \"error: The sandbox is not in sync with the Podfile.lock. Run 'pod install' or update your CocoaPods installation.\" >&2\n    exit 1\nfi\n# This output is used by Xcode 'outputs' to avoid re-running this script phase.\necho \"SUCCESS\" > \"${SCRIPT_OUTPUT_FILE_0}\"\n";
			showEnvVarsInLog = 0;
		};
		9B2D1113F56C486AFC287AFB /* [Expo] Configure project */ = {
			isa = PBXShellScriptBuildPhase;
			alwaysOutOfDate = 1;
			buildActionMask = **********;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
			);
			name = "[Expo] Configure project";
			outputFileListPaths = (
			);
			outputPaths = (
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "# This script configures Expo modules and generates the modules provider file.\nbash -l -c \"./Pods/Target\\ Support\\ Files/Pods-bookln/expo-configure-project.sh\"\n";
		};
		D7544E6ED07A9D823B79533E /* [CP] Embed Pods Frameworks */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-bookln/Pods-bookln-frameworks.sh",
				"${PODS_XCFRAMEWORKS_BUILD_DIR}/hermes-engine/Pre-built/hermes.framework/hermes",
			);
			name = "[CP] Embed Pods Frameworks";
			outputPaths = (
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/hermes.framework",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-bookln/Pods-bookln-frameworks.sh\"\n";
			showEnvVarsInLog = 0;
		};
/* End PBXShellScriptBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		13B07F871A680F5B00A75B9A /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = **********;
			files = (
				13B07FBC1A68108700A75B9A /* AppDelegate.mm in Sources */,
				13B07FC11A68108700A75B9A /* main.m in Sources */,
				2D2355B22B6F426800D9BB88 /* SetupExpoUpdates.swift in Sources */,
				B18059E884C0ABDD17F3DC3D /* ExpoModulesProvider.swift in Sources */,
				2D2355B82B6F428E00D9BB88 /* BooklnLaunchCount.m in Sources */,
				D770DC8C90B24BC885A19239 /* noop-file.swift in Sources */,
				2D442CE22BEA0FC700C45FA2 /* BooklnNativeHelper.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin XCBuildConfiguration section */
		13B07F941A680F5B00A75B9A /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = A2D6F2F1C005F3730125E7C6 /* Pods-bookln.debug.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				CLANG_ENABLE_MODULES = YES;
				CODE_SIGN_ENTITLEMENTS = bookln/bookln.entitlements;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = 2KT364D29M;
				ENABLE_BITCODE = NO;
				FRAMEWORK_SEARCH_PATHS = (
					"$(inherited)",
					"$(PROJECT_DIR)",
				);
				GCC_PREPROCESSOR_DEFINITIONS = (
					"$(inherited)",
					"FB_SONARKIT_ENABLED=1",
				);
				INFOPLIST_FILE = bookln/Info.plist;
				INFOPLIST_KEY_CFBundleDisplayName = "书链";
				IPHONEOS_DEPLOYMENT_TARGET = 13.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 8.1.0;
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-ObjC",
					"-lc++",
				);
				OTHER_SWIFT_FLAGS = "$(inherited) -D EXPO_CONFIGURATION_DEBUG";
				PRODUCT_BUNDLE_IDENTIFIER = com.yunti.zzm;
				PRODUCT_NAME = bookln;
				SWIFT_OBJC_BRIDGING_HEADER = "bookln/bookln-Bridging-Header.h";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				VERSIONING_SYSTEM = "apple-generic";
			};
			name = Debug;
		};
		13B07F951A680F5B00A75B9A /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 9ACE5613C882902A545E8300 /* Pods-bookln.release.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				CLANG_ENABLE_MODULES = YES;
				CODE_SIGN_ENTITLEMENTS = bookln/bookln.entitlements;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = 2KT364D29M;
				FRAMEWORK_SEARCH_PATHS = (
					"$(inherited)",
					"$(PROJECT_DIR)",
				);
				INFOPLIST_FILE = bookln/Info.plist;
				INFOPLIST_KEY_CFBundleDisplayName = "书链";
				IPHONEOS_DEPLOYMENT_TARGET = 13.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 8.1.0;
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-ObjC",
					"-lc++",
				);
				OTHER_SWIFT_FLAGS = "$(inherited) -D EXPO_CONFIGURATION_RELEASE";
				PRODUCT_BUNDLE_IDENTIFIER = com.yunti.zzm;
				PRODUCT_NAME = bookln;
				SWIFT_OBJC_BRIDGING_HEADER = "bookln/bookln-Bridging-Header.h";
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				VERSIONING_SYSTEM = "apple-generic";
			};
			name = Release;
		};
		83CBBA201A601CBA00E9B192 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED = YES;
				CLANG_CXX_LANGUAGE_STANDARD = "c++17";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				COPY_PHASE_STRIP = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				"EXCLUDED_ARCHS[sdk=iphonesimulator*]" = i386;
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
					_LIBCPP_ENABLE_CXX17_REMOVED_UNARY_BINARY_FUNCTION,
				);
				GCC_SYMBOLS_PRIVATE_EXTERN = NO;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 15.6;
				LD_RUNPATH_SEARCH_PATHS = (
					/usr/lib/swift,
					"$(inherited)",
				);
				LIBRARY_SEARCH_PATHS = "$(SDKROOT)/usr/lib/swift\"$(inherited)\"";
				MARKETING_VERSION = 1.2.1;
				MTL_ENABLE_DEBUG_INFO = YES;
				ONLY_ACTIVE_ARCH = YES;
				OTHER_CFLAGS = "$(inherited)";
				OTHER_CPLUSPLUSFLAGS = "$(inherited)";
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-Wl",
					"-ld_classic",
				);
				REACT_NATIVE_PATH = "${PODS_ROOT}/../../../../node_modules/react-native";
				SDKROOT = iphoneos;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Debug;
		};
		83CBBA211A601CBA00E9B192 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED = YES;
				CLANG_CXX_LANGUAGE_STANDARD = "c++17";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				COPY_PHASE_STRIP = YES;
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				"EXCLUDED_ARCHS[sdk=iphonesimulator*]" = i386;
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"$(inherited)",
					_LIBCPP_ENABLE_CXX17_REMOVED_UNARY_BINARY_FUNCTION,
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 15.6;
				LD_RUNPATH_SEARCH_PATHS = (
					/usr/lib/swift,
					"$(inherited)",
				);
				LIBRARY_SEARCH_PATHS = "$(SDKROOT)/usr/lib/swift\"$(inherited)\"";
				MARKETING_VERSION = 1.2.1;
				MTL_ENABLE_DEBUG_INFO = NO;
				OTHER_CFLAGS = "$(inherited)";
				OTHER_CPLUSPLUSFLAGS = "$(inherited)";
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-Wl",
					"-ld_classic",
				);
				REACT_NATIVE_PATH = "${PODS_ROOT}/../../../../node_modules/react-native";
				SDKROOT = iphoneos;
				TARGETED_DEVICE_FAMILY = "1,2";
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		13B07F931A680F5B00A75B9A /* Build configuration list for PBXNativeTarget "bookln" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				13B07F941A680F5B00A75B9A /* Debug */,
				13B07F951A680F5B00A75B9A /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		83CBB9FA1A601CBA00E9B192 /* Build configuration list for PBXProject "bookln" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				83CBBA201A601CBA00E9B192 /* Debug */,
				83CBBA211A601CBA00E9B192 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = 83CBB9F71A601CBA00E9B192 /* Project object */;
}
