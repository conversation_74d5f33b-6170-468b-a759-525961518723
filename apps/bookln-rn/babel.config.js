module.exports = function (api) {
  api.cache(true);
  return {
    presets: ['babel-preset-expo'],
    plugins: [
      'jotai/babel/plugin-react-refresh',

      // https://www.nativewind.dev/quick-starts/expo#3-add-the-babel-plugin
      'nativewind/babel',

      // Required for expo-router
      'expo-router/babel',

      // react-native-reanimated
      'react-native-reanimated/plugin',

      // https://github.com/facebook/react-native/issues/36828#issuecomment-2020940725
      // https://github.com/jfilter/react-native-onboarding-swiper/issues/144#issuecomment-1679250385
      '@babel/plugin-transform-flow-strip-types',
      ['@babel/plugin-transform-private-methods', { loose: true }],
    ],
    exclude: ['**/*.taro.{ts,tsx}'],
  };
};
