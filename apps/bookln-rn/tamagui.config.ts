import { config } from '@tamagui/config/v2-reanimated';
import { Platform } from 'react-native';
import { createFont, createTamagui } from 'tamagui';

config.fonts = {
  ...config.fonts,
  body: createFont({
    ...config.fonts.body,
    family: Platform.select({
      ios: 'System',
      android: 'Roboto',
      default: 'System',
    }),
  }),
};

export const tamaguiConfig = createTamagui(config);

export type AppConfig = typeof tamaguiConfig;

declare module 'tamagui' {
  interface TamaguiCustomConfig extends AppConfig {}
}
