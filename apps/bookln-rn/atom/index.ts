import { store } from '@jgl/biz-func';
import { storage } from '@jgl/utils';
import type { UsersApiUserInfoDTO } from '@yunti-private/api-xingdeng-boot';
import type { ExpoUpdateConfig } from '@yunti-private/rn-expo-updates-helper';
import { atom } from 'jotai';
import { atomWithStorage } from 'jotai/utils';
import { Platform } from 'react-native';
import type { BookDTO, RemoteUpdateConfDTO } from '../api/dto';
import type { AppVersionDTO, MarketDTO } from '../dtos/AppVersion';
import type { AiBook, AiBookTopicItem, TakePhotoAnswerResult } from '../hooks/aiBook';
import type { DownloadStatus, ExpoUpdatesInfoOnLaunch } from '../types';
import { storageKeys } from '../utils/constants';
import { native } from '../utils/Native';
import { LessonDTO } from '../dtos/LessonDTO';

/**
 * @deprecate 使用 @jgl/jgl-biz atoms 替代。@jgl/jgl-biz 中包含了Taro、React Native 的跨端代码。
 */
export const atomMap = {
  /**
   * 用户个人资料
   */
  profileAtom: atom<UsersApiUserInfoDTO | undefined>(undefined),

  /**
   * 检查app版本
   */
  checkedAppVersionResultAtom: atom<{
    appVersionDTO?: AppVersionDTO;
    downloadStatus?: DownloadStatus;
    sysCheck?: boolean;
    market?: MarketDTO;
    isShowUpgradeModal: boolean;
  }>({ isShowUpgradeModal: false }),

  /**
   * 热更新配置
   */
  updateConfigAtom: atom<ExpoUpdateConfig | undefined>(undefined),
  /**
   * 远端热更新配置项
   */
  remoteUpdateConf: atom<RemoteUpdateConfDTO | undefined>(undefined),
  /**
   * 启动时热更新 SDK 返回信息
   */
  updateInfoOnLaunch: atom<ExpoUpdatesInfoOnLaunch | undefined>(undefined),

  isSentryInitializedAtom: atom(false),

  /** 当前渠道 */
  currentChannelAtom: atom<string | undefined>(undefined),

  /** 字体加载结果：成功/失败 */
  fontLoadResultAtom: atom<{
    success: boolean;
    error: Error | null;
  }>({ success: false, error: null }),

  /** 开屏页是否已经被隐藏 */
  splashScreenHiddenAtom: atom(false),
  /** 首页标签 */
  homeTabIndexAtom: atom<number>(0),
  /** 是否已经初始化网络容器 */
  isNetContainerInitializedAtom: atom(false),
  aiBook: atom<AiBook | undefined>(undefined),
  aiBookInfo: atom<{
    pageId: number | undefined;
    topicId: number | undefined;
    bookId: number | undefined;
  }>({
    pageId: undefined,
    topicId: undefined,
    bookId: undefined,
  }),
  aiBookTakePhotoResult: atom<{
    showResultPages: Set<number>;
    answerResults: TakePhotoAnswerResult[];
    wrongResults: TakePhotoAnswerResult[];
    totalCount: number;
    rightCount: number;
    wrongCount: number;
  }>({
    showResultPages: new Set<number>(),
    answerResults: [],
    wrongResults: [],
    totalCount: 0,
    rightCount: 0,
    wrongCount: 0,
  }),
  aiBookMockData: atom<{
    collectionTopics: AiBookTopicItem[];
    wrongTopics: AiBookTopicItem[];
    topics: AiBookTopicItem[];
  }>({
    collectionTopics: [],
    wrongTopics: [],
    topics: [],
  }),

  aiBookTopicDetailHeightAtom: atom<number>(0),
};

/** 当前渠道 */
export const currentChannelAtom = atomWithStorage<string>(storageKeys.currentChannel, Platform.OS, {
  setItem: async (key, newValue) => {
    await storage.setItem(key, String(newValue), { env: false });
  },
  getItem: async (key, initialValue) => {
    let result = await storage.getItem(key, { env: false });
    if (!result) {
      result = await native.getCurrentAndroidChannel();
    }
    return result || initialValue;
  },
  removeItem: async (key) => {
    await storage.removeItem(key, { env: false });
  },
});
// 是否第一次进入首页
export const isFirstEnterHomeAtom = atomWithStorage<boolean | undefined | null>(
  storageKeys.isFirstEnterHome,
  undefined,
  {
    setItem: async (key, newValue) => {
      await storage.setItem(key, String(newValue), { env: false });
    },
    getItem: async (key, initialValue) => {
      const result = await storage.getItem(key, { env: false });
      return result !== 'false';
    },
    removeItem: async (key) => {
      await storage.removeItem(key, { env: false });
    },
  },
);

const bookShelfKey = () => {
  const userId = store.getState().userInfo.userId;
  return `${storageKeys.bookShelf}-${userId}`;
};

/** 书架 */
export const bookShelfAtom = atomWithStorage<BookDTO[]>(bookShelfKey(), [], {
  setItem: async (key, newValue) => {
    await storage.setItem(key, JSON.stringify(newValue), { env: false });
  },
  getItem: async (key, initialValue) => {
    const result = await storage.getItem(key, { env: false });
    return result ? JSON.parse(result) : initialValue;
  },
  removeItem: async (key) => {
    await storage.removeItem(key, { env: false });
  },
});

const myLessonsKey = () => {
  const userId = store.getState().userInfo.userId;
  return `${storageKeys.myLessons}-${userId}`;
};

/** 我的课程 */
export const myLessonsAtom = atomWithStorage<LessonDTO[]>(myLessonsKey(), [], {
  setItem: async (key, newValue) => {
    await storage.setItem(key, JSON.stringify(newValue), { env: false });
  },
  getItem: async (key, initialValue) => {
    const result = await storage.getItem(key, { env: false });
    return result ? JSON.parse(result) : initialValue;
  },
  removeItem: async (key) => {
    await storage.removeItem(key, { env: false });
  },
});

/** 个性化开关状态 */
export const personalizedSwitchAtom = atomWithStorage<boolean>(
  storageKeys.personalizedSwitch,
  true,
  {
    setItem: async (key, newValue) => {
      await storage.setItem(key, String(newValue), { env: false });
    },
    getItem: async (key, initialValue) => {
      const result = await storage.getItem(key, { env: false });
      if (result !== undefined) {
        return result !== 'false';
      }
      return initialValue;
    },
    removeItem: async (key) => {
      await storage.removeItem(key, { env: false });
    },
  },
);

/** 消息通知开关状态 */
export const messageNotificationSwitchAtom = atomWithStorage<boolean>(
  storageKeys.messageNotificationSwitch,
  true,
  {
    setItem: async (key, newValue) => {
      await storage.setItem(key, String(newValue), { env: false });
    },
    getItem: async (key, initialValue) => {
      const result = await storage.getItem(key, { env: false });
      if (result !== undefined) {
        return result !== 'false';
      }
      return initialValue;
    },
    removeItem: async (key) => {
      await storage.removeItem(key, { env: false });
    },
  },
);

/** 是否显示 AI 聊天浮窗 */
// TODO: leejunhui - 默认应该隐藏 (2025_05_20)
export const aiChatFloatBallVisibleAtom = atom<boolean>(true);
